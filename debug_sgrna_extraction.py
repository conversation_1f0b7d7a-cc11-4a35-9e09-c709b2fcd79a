#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
调试sgRNA提取问题的脚本
"""

import pandas as pd
import gzip
import re

def check_library_file(library_file):
    """检查库文件"""
    print("=== 检查库文件 ===")
    df = pd.read_csv(library_file)
    print("库文件列名:", list(df.columns))
    print("总sgRNA数量:", len(df))
    print("前5个sgRNA:")
    for i, row in df.head().iterrows():
        print("  %s: %s (%d bp)" % (row['sgRNA_ID'], row['sgRNA_sequence'], len(row['sgRNA_sequence'])))
    
    lengths = df['sgRNA_sequence'].str.len()
    print("sgRNA长度范围: %d-%d" % (lengths.min(), lengths.max()))
    return df

def check_extracted_fastq(fastq_file):
    """检查提取的FASTQ文件"""
    print("\n=== 检查提取的FASTQ文件 ===")
    print("文件:", fastq_file)
    
    sequences = []
    
    if fastq_file.endswith('.gz'):
        file_handle = gzip.open(fastq_file, 'rb')
    else:
        file_handle = open(fastq_file, 'r')
    
    try:
        count = 0
        while True and count < 10:  # 只检查前10个
            header = file_handle.readline()
            if not header:
                break
            
            if isinstance(header, bytes):
                header = header.decode('utf-8')
            
            sequence = file_handle.readline()
            if isinstance(sequence, bytes):
                sequence = sequence.decode('utf-8')
            sequence = sequence.strip()
            
            plus = file_handle.readline()
            quality = file_handle.readline()
            
            sequences.append(sequence)
            count += 1
            print("序列 #%d: %s (%d bp)" % (count, sequence, len(sequence)))
    
    finally:
        file_handle.close()
    
    if sequences:
        lengths = [len(s) for s in sequences]
        print("提取序列长度范围: %d-%d" % (min(lengths), max(lengths)))
    
    return sequences

def check_sequence_match(library_df, extracted_sequences):
    """检查提取的序列是否在库中"""
    print("\n=== 检查序列匹配 ===")
    
    library_seqs = set(library_df['sgRNA_sequence'].tolist())
    print("库中sgRNA数量:", len(library_seqs))
    
    matches = 0
    for i, seq in enumerate(extracted_sequences[:10]):
        if seq in library_seqs:
            matches += 1
            print("序列 #%d: %s - 匹配 ✓" % (i+1, seq))
        else:
            print("序列 #%d: %s - 不匹配 ✗" % (i+1, seq))
            
            # 检查是否有相似的序列
            for lib_seq in list(library_seqs)[:5]:
                if len(seq) == len(lib_seq):
                    mismatches = sum(1 for a, b in zip(seq, lib_seq) if a != b)
                    if mismatches <= 2:
                        print("    相似序列: %s (错配:%d)" % (lib_seq, mismatches))
                        break
    
    print("匹配率: %d/%d (%.1f%%)" % (matches, len(extracted_sequences), 100.0 * matches / len(extracted_sequences) if extracted_sequences else 0))

def test_extraction_pattern(test_sequence, upstream, downstream, min_len, max_len):
    """测试提取模式"""
    print("\n=== 测试提取模式 ===")
    print("测试序列:", test_sequence)
    print("上游引物:", upstream)
    print("下游引物:", downstream)
    print("长度范围: %d-%d" % (min_len, max_len))
    
    # 正向搜索
    for sgrna_len in range(min_len, max_len + 1):
        pattern = upstream + '(.{%d})' % sgrna_len + downstream
        match = re.search(pattern, test_sequence)
        if match:
            extracted = match.group(1)
            print("正向匹配 (长度%d): %s" % (sgrna_len, extracted))
            return extracted
    
    # 反向互补搜索
    def reverse_complement(seq):
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(seq.upper()))
    
    upstream_rc = reverse_complement(upstream)
    downstream_rc = reverse_complement(downstream)
    
    for sgrna_len in range(min_len, max_len + 1):
        pattern = downstream_rc + '(.{%d})' % sgrna_len + upstream_rc
        match = re.search(pattern, test_sequence)
        if match:
            extracted = reverse_complement(match.group(1))
            print("反向互补匹配 (长度%d): %s" % (sgrna_len, extracted))
            return extracted
    
    print("未找到匹配")
    return None

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 4:
        print("用法: python debug_sgrna_extraction.py <library.csv> <extracted.fq> <upstream> <downstream>")
        print("示例: python debug_sgrna_extraction.py PL808-NGS/lib.csv PL808-NGS/PL808-NGS.fq CACCG GTTTTAGAGCTAGAAATAGC")
        sys.exit(1)
    
    library_file = sys.argv[1]
    fastq_file = sys.argv[2]
    upstream = sys.argv[3]
    downstream = sys.argv[4]
    
    print("调试sgRNA提取问题")
    print("=" * 50)
    
    # 检查库文件
    library_df = check_library_file(library_file)
    
    # 检查提取的FASTQ
    extracted_sequences = check_extracted_fastq(fastq_file)
    
    # 检查匹配
    if extracted_sequences:
        check_sequence_match(library_df, extracted_sequences)
    
    # 测试提取模式
    if extracted_sequences:
        # 从原始reads中构造一个测试序列
        test_seq = upstream + library_df['sgRNA_sequence'].iloc[0] + downstream + "EXTRA"
        print("\n构造测试序列:", test_seq)
        min_len = library_df['sgRNA_sequence'].str.len().min()
        max_len = library_df['sgRNA_sequence'].str.len().max()
        test_extraction_pattern(test_seq, upstream, downstream, min_len, max_len)

if __name__ == '__main__':
    main()
