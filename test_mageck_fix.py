#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
测试MAGeCK修复是否有效
"""

import subprocess
import pandas as pd
import os

def test_library_format(library_file):
    """测试库文件格式"""
    print("=== 测试库文件格式 ===")
    print("文件: %s" % library_file)
    
    if not os.path.exists(library_file):
        print("错误: 文件不存在")
        return False
    
    # 读取文件
    df = pd.read_csv(library_file)
    print("列数: %d" % len(df.columns))
    print("列名: %s" % list(df.columns))
    print("行数: %d" % len(df))
    
    # 检查是否有必需的列
    required_cols = ['sgRNA_ID', 'sgRNA_sequence', 'Gene']
    missing_cols = [col for col in required_cols if col not in df.columns]
    
    if missing_cols:
        print("缺失列: %s" % missing_cols)
        return False
    
    print("前3行:")
    for i, row in df.head(3).iterrows():
        print("  %s,%s,%s" % (row['sgRNA_ID'], row['sgRNA_sequence'], row['Gene']))
    
    return True

def test_mageck_command(library_file, bam_file, sample_name):
    """测试MAGeCK命令"""
    print("\n=== 测试MAGeCK命令 ===")
    
    # 测试不同的MAGeCK命令格式
    commands_to_test = [
        # 标准格式
        [
            'mageck', 'count',
            '-l', library_file,
            '-n', sample_name,
            '--sample-label', sample_name,
            '--fastq', bam_file
        ],
        # 带unmapped-to-file
        [
            'mageck', 'count',
            '-l', library_file,
            '-n', sample_name,
            '--sample-label', sample_name,
            '--fastq', bam_file,
            '--unmapped-to-file', sample_name
        ]
    ]
    
    for i, cmd in enumerate(commands_to_test):
        print("\n测试命令 %d: %s" % (i+1, ' '.join(cmd)))
        
        try:
            # 只测试命令是否能解析，不实际运行
            result = subprocess.Popen(cmd + ['--help'], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
            stdout, stderr = result.communicate()
            
            if result.returncode == 0:
                print("  命令格式正确 ✓")
            else:
                print("  命令格式错误 ✗")
                if stderr:
                    print("  错误: %s" % stderr.decode('utf-8')[:200])
        
        except Exception as e:
            print("  命令执行失败: %s" % str(e))

def create_test_library():
    """创建测试库文件"""
    print("=== 创建测试库文件 ===")
    
    data = {
        'sgRNA_ID': ['gRNA_1', 'gRNA_2', 'gRNA_3'],
        'sgRNA_sequence': ['CCTGGCACCCCGACCTCCGG', 'CGAAAGGCACCAAAGTGGTC', 'AAAGCGAGTACAGCGTGAAG'],
        'Gene': ['Gene1', 'Gene2', 'Gene3']
    }
    
    df = pd.DataFrame(data)
    test_file = 'test_lib.csv'
    df.to_csv(test_file, index=False)
    
    print("创建测试文件: %s" % test_file)
    return test_file

def main():
    import sys
    
    print("MAGeCK修复测试")
    print("=" * 50)
    
    if len(sys.argv) >= 2:
        library_file = sys.argv[1]
    else:
        library_file = create_test_library()
    
    # 测试库文件格式
    if test_library_format(library_file):
        print("库文件格式正确 ✓")
    else:
        print("库文件格式错误 ✗")
        return
    
    # 测试MAGeCK命令（如果提供了BAM文件）
    if len(sys.argv) >= 3:
        bam_file = sys.argv[2]
        sample_name = sys.argv[3] if len(sys.argv) >= 4 else 'test_sample'
        test_mageck_command(library_file, bam_file, sample_name)
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == '__main__':
    main()
