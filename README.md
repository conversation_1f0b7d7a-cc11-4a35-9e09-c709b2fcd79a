# sgRNA Analysis Pipeline

A comprehensive Python2 pipeline for analyzing sgRNA library sequencing data.

## Features

- **Excel to CSV conversion**: Converts sgRNA library Excel files to CSV format
- **Read merging**: Uses FLASH to merge paired-end reads
- **sgRNA extraction**: Extracts sgRNA sequences using flanking sequences
- **Alignment**: Uses bowtie2 for alignment against reference library
- **Counting**: Uses MAGeCK for read counting and analysis
- **Quality analysis**: Analyzes mismatches, indels, and base error rates
- **Statistical analysis**: Calculates skew ratio and coverage statistics
- **Visualization**: Generates cumulative distribution plots
- **Report generation**: Creates comprehensive Excel reports

## Requirements

### Software Dependencies
- Python 2.7
- FLASH (for read merging)
- bowtie2 (for alignment)
- samtools (for BAM processing)
- MAGeCK (for counting analysis)

### Python Dependencies
- pandas
- numpy
- matplotlib
- pysam
- openpyxl (for Excel file handling)

## Installation

1. Install the required software tools:
```bash
# Install FLASH
# Download from: https://ccb.jhu.edu/software/FLASH/

# Install bowtie2
# Download from: http://bowtie-bio.sourceforge.net/bowtie2/

# Install samtools
# Download from: http://www.htslib.org/

# Install MAGeCK
pip install mageck
```

2. Install Python dependencies:
```bash
pip install pandas numpy matplotlib pysam openpyxl
```

## Usage

### Basic Usage

```bash
python sgrna_analysis_pipeline.py <excel_file> <r1_fastq> <r2_fastq> <flanking_sequences>
```

### Parameters

- `excel_file`: sgRNA library Excel file (first column: sgRNA ID, second column: sgRNA sequence)
- `r1_fastq`: R1 FASTQ file (can be .gz compressed)
- `r2_fastq`: R2 FASTQ file (can be .gz compressed)
- `flanking_sequences`: Upstream and downstream sequences in format "upstream-downstream"

### Optional Parameters

- `-o, --output_dir`: Output directory (default: current directory)
- `-s, --sample_name`: Sample name (default: extracted from R1 filename)

### Example

```bash
python sgrna_analysis_pipeline.py \
    library.xlsx \
    sample_R1.fastq.gz \
    sample_R2.fastq.gz \
    "CACCG-GTTTTAGAGCTAGAAATAGC" \
    -o results/ \
    -s my_sample
```

## Output Files

The pipeline generates the following output files:

### Primary Outputs
- `lib.csv`: Converted library file in CSV format
- `Sample.stat.xls`: Final summary statistics report
- `<sample>.png`: Cumulative distribution plot

### Intermediate Files
- `<sample>.extendedFrags.fastq`: Merged reads from FLASH
- `<sample>.notCombined_1.fastq`: Unmerged R1 reads
- `<sample>.notCombined_2.fastq`: Unmerged R2 reads
- `<sample>.combined.fq`: Extracted sgRNAs from merged reads
- `<sample>.nocombined.fq`: Extracted sgRNAs from unmerged reads
- `<sample>.fq`: Combined extracted sgRNAs
- `reference_sgrna.fasta`: Reference FASTA file
- `<sample>.bam`: Alignment results
- `<sample>.count.txt`: MAGeCK count results
- `<sample>.log`: MAGeCK log file
- `mismatch_analysis.csv`: Mismatch analysis results

## Output Statistics

The final report (`Sample.stat.xls`) contains:

- **Sequencing Reads Amount**: Total number of input reads
- **Sequencing Depth**: Average reads per sgRNA
- **Correct Sequence rate**: Percentage of reads that aligned
- **Total gRNAs**: Number of sgRNAs in library
- **Zero gRNAs**: Number of sgRNAs with zero reads
- **Coverage_rate**: Percentage of sgRNAs with at least one read
- **Skew Ration**: Distribution skewness (90th percentile / 10th percentile)
- **Base Error rate**: Overall base error rate including mismatches and indels

## Algorithm Details

### sgRNA Extraction

The pipeline extracts sgRNA sequences by:

1. **Forward orientation search**: `upstream[sgRNA]downstream`
2. **Reverse complement search**: `downstream_rc[sgRNA_rc]upstream_rc`

For unmerged reads, it searches:
1. R1 with both orientations
2. If not found, searches corresponding R2 with both orientations

### Skew Ratio Calculation

The skew ratio measures library distribution uniformity:
- Sorts sgRNAs by read count (descending)
- Finds positions where cumulative reads reach 10% and 90%
- Calculates ratio: (position at 90%) / (position at 10%)

### Base Error Rate

Calculated as: `total_mismatches / total_bases`

Where:
- `total_mismatches = 1×(1-mismatch) + 2×(2-mismatch) + ... + 5×(5-mismatch) + 1×(indels)`
- `total_bases = Σ(sgRNA_length + flanking_length) × read_count`

## Troubleshooting

### Common Issues

1. **FLASH not found**: Ensure FLASH is installed and in PATH
2. **bowtie2 not found**: Ensure bowtie2 is installed and in PATH
3. **Memory issues**: Large FASTQ files may require more RAM
4. **Excel reading errors**: Ensure Excel file has sgRNA ID in column 1 and sequence in column 2

### Performance Tips

- Use compressed FASTQ files (.gz) to save disk space
- Ensure sufficient disk space for intermediate files
- For large libraries, consider running on a high-memory system

## License

This pipeline is provided as-is for research purposes.
