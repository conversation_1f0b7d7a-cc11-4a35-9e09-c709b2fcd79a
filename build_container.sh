#!/bin/bash

# sgRNA Analysis Pipeline Container Builder
# This script builds a Singularity container with all required dependencies

set -e

echo "=========================================="
echo "sgRNA Analysis Pipeline Container Builder"
echo "=========================================="

# Check if Singularity is installed
if ! command -v singularity &> /dev/null; then
    echo "Error: Singularity is not installed or not in PATH"
    echo "Please install Singularity first:"
    echo "  https://sylabs.io/guides/3.0/user-guide/installation.html"
    exit 1
fi

echo "Singularity version:"
singularity --version

# Check if definition file exists
if [ ! -f "sgrna_analysis.def" ]; then
    echo "Error: sgrna_analysis.def not found"
    echo "Please ensure the definition file is in the current directory"
    exit 1
fi

# Check if Python scripts exist
if [ ! -f "sgrna_analysis_pipeline.py" ]; then
    echo "Error: sgrna_analysis_pipeline.py not found"
    exit 1
fi

if [ ! -f "sgrna_bowtie2_analysis_mis5.py" ]; then
    echo "Error: sgrna_bowtie2_analysis_mis5.py not found"
    exit 1
fi

echo ""
echo "Building Singularity container..."
echo "This may take 10-20 minutes depending on your internet connection"
echo ""

# Build the container
singularity build sgrna_analysis.sif sgrna_analysis.def

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "Container built successfully!"
    echo "=========================================="
    echo ""
    echo "Container file: sgrna_analysis.sif"
    echo "Size: $(du -h sgrna_analysis.sif | cut -f1)"
    echo ""
    echo "Test the container:"
    echo "  singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py --help"
    echo ""
    echo "Run analysis:"
    echo "  singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py \\"
    echo "    library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream' -o results -s sample"
    echo ""
else
    echo ""
    echo "=========================================="
    echo "Container build failed!"
    echo "=========================================="
    echo "Please check the error messages above"
    exit 1
fi
