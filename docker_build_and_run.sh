#!/bin/bash

# sgRNA Analysis Pipeline Docker Builder and Runner

set -e

IMAGE_NAME="sgrna-analysis"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

# Function to show usage
show_usage() {
    echo "sgRNA Analysis Pipeline Docker Manager"
    echo ""
    echo "Usage:"
    echo "  $0 build                    Build Docker image"
    echo "  $0 run [OPTIONS]            Run analysis with Docker"
    echo "  $0 shell                    Start interactive shell in container"
    echo "  $0 test                     Run test with sample data"
    echo ""
    echo "Run options:"
    echo "  -l, --library LIBRARY       sgRNA library Excel file"
    echo "  -1, --r1 R1_FASTQ           R1 FASTQ file"
    echo "  -2, --r2 R2_FASTQ           R2 FASTQ file"
    echo "  -f, --flanking FLANKING     Flanking sequences"
    echo "  -o, --output OUTPUT_DIR     Output directory (default: results)"
    echo "  -s, --sample SAMPLE_NAME    Sample name"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 run -l lib.xlsx -1 R1.fq.gz -2 R2.fq.gz -f 'UP-DOWN' -o results"
    echo "  $0 test"
}

# Function to build Docker image
build_image() {
    echo "=========================================="
    echo "Building Docker image: $FULL_IMAGE_NAME"
    echo "=========================================="
    
    if [ ! -f "Dockerfile" ]; then
        echo "Error: Dockerfile not found"
        exit 1
    fi
    
    if [ ! -f "sgrna_analysis_pipeline.py" ]; then
        echo "Error: sgrna_analysis_pipeline.py not found"
        exit 1
    fi
    
    if [ ! -f "sgrna_bowtie2_analysis_mis5.py" ]; then
        echo "Error: sgrna_bowtie2_analysis_mis5.py not found"
        exit 1
    fi
    
    docker build -t "$FULL_IMAGE_NAME" .
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "=========================================="
        echo "Docker image built successfully!"
        echo "=========================================="
        echo "Image: $FULL_IMAGE_NAME"
        echo "Size: $(docker images $FULL_IMAGE_NAME --format "table {{.Size}}" | tail -n 1)"
        echo ""
        echo "Test the image:"
        echo "  $0 test"
        echo ""
    else
        echo "Docker build failed!"
        exit 1
    fi
}

# Function to run analysis
run_analysis() {
    local LIBRARY=""
    local R1_FASTQ=""
    local R2_FASTQ=""
    local FLANKING=""
    local OUTPUT_DIR="results"
    local SAMPLE_NAME=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -l|--library)
                LIBRARY="$2"
                shift 2
                ;;
            -1|--r1)
                R1_FASTQ="$2"
                shift 2
                ;;
            -2|--r2)
                R2_FASTQ="$2"
                shift 2
                ;;
            -f|--flanking)
                FLANKING="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            -s|--sample)
                SAMPLE_NAME="$2"
                shift 2
                ;;
            *)
                echo "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check required arguments
    if [ -z "$LIBRARY" ] || [ -z "$R1_FASTQ" ] || [ -z "$R2_FASTQ" ] || [ -z "$FLANKING" ]; then
        echo "Error: Missing required arguments"
        show_usage
        exit 1
    fi
    
    # Check if files exist
    for file in "$LIBRARY" "$R1_FASTQ" "$R2_FASTQ"; do
        if [ ! -f "$file" ]; then
            echo "Error: File not found: $file"
            exit 1
        fi
    done
    
    # Create output directory
    mkdir -p "$OUTPUT_DIR"
    
    # Get absolute paths
    LIBRARY_ABS=$(realpath "$LIBRARY")
    R1_FASTQ_ABS=$(realpath "$R1_FASTQ")
    R2_FASTQ_ABS=$(realpath "$R2_FASTQ")
    OUTPUT_DIR_ABS=$(realpath "$OUTPUT_DIR")
    
    # Extract sample name if not provided
    if [ -z "$SAMPLE_NAME" ]; then
        SAMPLE_NAME=$(basename "$R1_FASTQ" | sed 's/_R1.*$//' | sed 's/\.fastq.*$//' | sed 's/\.fq.*$//')
    fi
    
    echo "=========================================="
    echo "Running sgRNA Analysis with Docker"
    echo "=========================================="
    echo "Library:     $LIBRARY_ABS"
    echo "R1 FASTQ:    $R1_FASTQ_ABS"
    echo "R2 FASTQ:    $R2_FASTQ_ABS"
    echo "Flanking:    $FLANKING"
    echo "Output:      $OUTPUT_DIR_ABS"
    echo "Sample:      $SAMPLE_NAME"
    echo "=========================================="
    
    # Run Docker container
    docker run --rm \
        -v "$OUTPUT_DIR_ABS:/data/output" \
        -v "$(dirname "$LIBRARY_ABS"):/data/library" \
        -v "$(dirname "$R1_FASTQ_ABS"):/data/fastq" \
        -w /data/output \
        "$FULL_IMAGE_NAME" \
        "/data/library/$(basename "$LIBRARY_ABS")" \
        "/data/fastq/$(basename "$R1_FASTQ_ABS")" \
        "/data/fastq/$(basename "$R2_FASTQ_ABS")" \
        "$FLANKING" \
        -o /data/output \
        -s "$SAMPLE_NAME"
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "=========================================="
        echo "Analysis completed successfully!"
        echo "=========================================="
        echo "Output directory: $OUTPUT_DIR_ABS"
        echo ""
    else
        echo "Analysis failed!"
        exit 1
    fi
}

# Function to start interactive shell
start_shell() {
    echo "Starting interactive shell in container..."
    docker run --rm -it \
        -v "$(pwd):/data" \
        -w /data \
        "$FULL_IMAGE_NAME" \
        /bin/bash
}

# Function to run test
run_test() {
    echo "=========================================="
    echo "Running Docker container test"
    echo "=========================================="
    
    # Check if image exists
    if ! docker images | grep -q "$IMAGE_NAME"; then
        echo "Docker image not found. Building first..."
        build_image
    fi
    
    # Test basic functionality
    echo "Testing container functionality..."
    docker run --rm "$FULL_IMAGE_NAME" --help
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "Container test passed!"
        echo "You can now run analysis with real data using:"
        echo "  $0 run -l library.xlsx -1 R1.fastq.gz -2 R2.fastq.gz -f 'upstream-downstream'"
    else
        echo "Container test failed!"
        exit 1
    fi
}

# Main script logic
case "${1:-}" in
    build)
        build_image
        ;;
    run)
        shift
        run_analysis "$@"
        ;;
    shell)
        start_shell
        ;;
    test)
        run_test
        ;;
    -h|--help|help|"")
        show_usage
        ;;
    *)
        echo "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
