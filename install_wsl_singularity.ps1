# PowerShell script to install WSL2 and Singularity for sgRNA analysis
# Run as Administrator

Write-Host "=========================================="
Write-Host "Installing WSL2 and Singularity for sgRNA Analysis"
Write-Host "=========================================="

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "Error: This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Enable WSL and Virtual Machine Platform
Write-Host "Enabling WSL and Virtual Machine Platform..." -ForegroundColor Green
dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

# Download and install WSL2 kernel update
Write-Host "Downloading WSL2 kernel update..." -ForegroundColor Green
$wslUpdateUrl = "https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi"
$wslUpdatePath = "$env:TEMP\wsl_update_x64.msi"

try {
    Invoke-WebRequest -Uri $wslUpdateUrl -OutFile $wslUpdatePath
    Start-Process msiexec.exe -Wait -ArgumentList "/i $wslUpdatePath /quiet"
    Remove-Item $wslUpdatePath
    Write-Host "WSL2 kernel update installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not download WSL2 kernel update. Please install manually." -ForegroundColor Yellow
}

# Set WSL2 as default version
Write-Host "Setting WSL2 as default version..." -ForegroundColor Green
wsl --set-default-version 2

Write-Host ""
Write-Host "=========================================="
Write-Host "WSL2 setup completed!"
Write-Host "=========================================="
Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Restart your computer"
Write-Host "2. Install Ubuntu from Microsoft Store or run:"
Write-Host "   wsl --install -d Ubuntu-20.04"
Write-Host "3. Run the Ubuntu setup script"
Write-Host ""
Write-Host "After restart, run this command to install Ubuntu:"
Write-Host "wsl --install -d Ubuntu-20.04" -ForegroundColor Cyan
Write-Host ""

# Create Ubuntu setup script
$ubuntuScript = @"
#!/bin/bash
# Ubuntu setup script for Singularity and sgRNA analysis

echo "=========================================="
echo "Setting up Ubuntu for sgRNA Analysis"
echo "=========================================="

# Update system
echo "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install dependencies
echo "Installing build dependencies..."
sudo apt install -y \
    build-essential \
    libssl-dev \
    uuid-dev \
    libgpgme11-dev \
    squashfs-tools \
    libseccomp-dev \
    wget \
    pkg-config \
    git \
    cryptsetup \
    curl

# Install Go
echo "Installing Go..."
export VERSION=1.19.3 OS=linux ARCH=amd64
cd /tmp
wget https://dl.google.com/go/go`$VERSION.`$OS-`$ARCH.tar.gz
sudo tar -C /usr/local -xzvf go`$VERSION.`$OS-`$ARCH.tar.gz
rm go`$VERSION.`$OS-`$ARCH.tar.gz

# Set up Go environment
echo 'export GOPATH=`${HOME}/go' >> ~/.bashrc
echo 'export PATH=/usr/local/go/bin:`${PATH}:`${GOPATH}/bin' >> ~/.bashrc
source ~/.bashrc

# Install Singularity
echo "Installing Singularity..."
export VERSION=3.10.4
cd /tmp
wget https://github.com/sylabs/singularity/releases/download/v`${VERSION}/singularity-ce-`${VERSION}.tar.gz
tar -xzf singularity-ce-`${VERSION}.tar.gz
cd singularity-ce-`${VERSION}

./mconfig
make -C builddir
sudo make -C builddir install

# Verify installation
echo ""
echo "=========================================="
echo "Installation completed!"
echo "=========================================="
echo "Singularity version:"
singularity --version

echo ""
echo "Next steps:"
echo "1. Copy your sgRNA analysis files to Ubuntu:"
echo "   cp /mnt/d/CRISPRQC/*.py ~/sgrna_analysis/"
echo "   cp /mnt/d/CRISPRQC/*.def ~/sgrna_analysis/"
echo "   cp /mnt/d/CRISPRQC/*.sh ~/sgrna_analysis/"
echo ""
echo "2. Build the container:"
echo "   cd ~/sgrna_analysis"
echo "   chmod +x *.sh"
echo "   ./build_container.sh"
echo ""
echo "3. Run your analysis:"
echo "   ./run_sgrna_analysis.sh -l library.xlsx -1 R1.fastq.gz -2 R2.fastq.gz -f 'CACCG-GTTTTAGAGCTAGAAATAGC'"
echo ""
"@

# Save Ubuntu setup script
$scriptPath = "$env:USERPROFILE\Desktop\setup_ubuntu_singularity.sh"
$ubuntuScript | Out-File -FilePath $scriptPath -Encoding UTF8

Write-Host "Ubuntu setup script saved to: $scriptPath" -ForegroundColor Green
Write-Host "After installing Ubuntu, run this script inside Ubuntu to install Singularity" -ForegroundColor Yellow
Write-Host ""
Write-Host "Please restart your computer now!" -ForegroundColor Red
