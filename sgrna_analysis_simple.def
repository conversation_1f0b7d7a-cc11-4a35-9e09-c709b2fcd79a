Bootstrap: docker
From: continuumio/miniconda3:4.10.3

%labels
    Author CRISPRQC Team
    Version 1.0
    Description sgRNA Analysis Pipeline - Prebuilt Version

%help
    This container includes sgRNA analysis tools.
    Usage: singularity exec container.sif python /opt/sgrna_analysis_pipeline.py [options]

%environment
    export PATH=/opt/conda/bin:/opt/tools:$PATH
    export PYTHONPATH=/opt:$PYTHONPATH

%post
    # Install Python 2.7 environment
    /opt/conda/bin/conda create -n py27 python=2.7 -y

    # Activate environment and install packages
    /opt/conda/bin/conda install -n py27 -c conda-forge -y \
        pandas=0.24.2 \
        numpy=1.16.6 \
        matplotlib=2.2.5 \
        openpyxl=2.6.4 \
        pip

    # Install pip packages in py27 environment
    /opt/conda/envs/py27/bin/pip install \
        pysam==0.15.4 \
        seaborn==0.9.1 \
        mageck==*******

    # Create tools directory
    mkdir -p /opt/tools

    # Download and install FLASH (static binary)
    cd /tmp
    wget -q https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
    cp FLASH-1.2.11-Linux-x86_64/flash /opt/tools/
    chmod +x /opt/tools/flash

    # Download and install bowtie2 (static binaries)
    wget -q https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip
    unzip -q bowtie2-2.4.5-linux-x86_64.zip
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* /opt/tools/
    chmod +x /opt/tools/bowtie2*

    # Download and install samtools (precompiled)
    wget -q https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2
    tar -xjf samtools-1.15.1.tar.bz2
    cd samtools-1.15.1
    make configure-htslib
    ./configure --prefix=/opt/tools --disable-bz2 --disable-lzma
    make -j2 && make install

    # Clean up
    cd /
    rm -rf /tmp/*
    /opt/conda/bin/conda clean -a -y

%files
    sgrna_analysis_pipeline.py /opt/
    sgrna_bowtie2_analysis_mis5.py /opt/

%runscript
    source /opt/conda/bin/activate py27
    exec python /opt/sgrna_analysis_pipeline.py "$@"
