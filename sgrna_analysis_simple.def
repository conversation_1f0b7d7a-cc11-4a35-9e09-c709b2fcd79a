Bootstrap: docker
From: continuumio/miniconda:latest

%labels
    Author CRISPRQC Team
    Version 1.0
    Description sgRNA Analysis Pipeline - Simple Version

%help
    This container includes sgRNA analysis tools.
    Usage: singularity exec container.sif python /opt/sgrna_analysis_pipeline.py [options]

%environment
    export PATH=/opt/conda/bin:/opt/tools:$PATH
    export PYTHONPATH=/opt:$PYTHONPATH

%post
    # Update conda
    conda update -n base -c defaults conda -y
    
    # Install Python 2.7 and packages
    conda create -n py27 python=2.7 -y
    conda activate py27
    
    # Install required packages
    conda install -c conda-forge -y \
        pandas=0.24.2 \
        numpy=1.16.6 \
        matplotlib=2.2.5 \
        openpyxl=2.6.4
    
    # Install pip packages
    pip install pysam==0.15.4 seaborn==0.9.1 mageck==*******
    
    # Create tools directory
    mkdir -p /opt/tools
    
    # Download and install FLASH
    cd /tmp
    wget -q https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
    cp FLASH-1.2.11-Linux-x86_64/flash /opt/tools/
    chmod +x /opt/tools/flash
    
    # Download and install bowtie2
    wget -q https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip
    unzip -q bowtie2-2.4.5-linux-x86_64.zip
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* /opt/tools/
    chmod +x /opt/tools/bowtie2*
    
    # Download and install samtools
    wget -q https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2
    tar -xjf samtools-1.15.1.tar.bz2
    cd samtools-1.15.1
    ./configure --prefix=/opt/tools
    make -j4 && make install
    
    # Clean up
    cd /
    rm -rf /tmp/*
    conda clean -a -y

%files
    sgrna_analysis_pipeline.py /opt/
    sgrna_bowtie2_analysis_mis5.py /opt/

%runscript
    source activate py27
    exec python /opt/sgrna_analysis_pipeline.py "$@"
