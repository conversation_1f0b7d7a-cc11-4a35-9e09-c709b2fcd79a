# sgRNA Analysis Pipeline - 容器化解决方案

## 概述

为了解决软件依赖问题，我们提供了两种容器化解决方案：
1. **Singularity容器** (推荐用于HPC环境)
2. **Docker容器** (推荐用于本地开发)

两种方案都包含了所有必需的软件依赖，无需手动安装FLASH、bowtie2、samtools等工具。

## 方案一：Singularity容器 (推荐)

### 优势
- 适合HPC集群环境
- 无需root权限运行
- 更好的安全性
- 支持GPU加速

### 构建容器

```bash
# 1. 确保有Singularity环境
singularity --version

# 2. 构建容器 (需要sudo权限或fakeroot)
sudo singularity build sgrna_analysis.sif sgrna_analysis.def

# 或者使用构建脚本
chmod +x build_container.sh
./build_container.sh
```

### 使用容器

**方法1: 使用包装脚本 (推荐)**
```bash
chmod +x run_sgrna_analysis.sh

# 运行分析
./run_sgrna_analysis.sh \
    -l library.xlsx \
    -1 sample_R1.fastq.gz \
    -2 sample_R2.fastq.gz \
    -f "CACCG-GTTTTAGAGCTAGAAATAGC" \
    -o results \
    -s sample_name
```

**方法2: 直接使用Singularity**
```bash
# 运行分析
singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py \
    library.xlsx \
    sample_R1.fastq.gz \
    sample_R2.fastq.gz \
    "CACCG-GTTTTAGAGCTAGAAATAGC" \
    -o results \
    -s sample_name

# 交互式shell
singularity shell sgrna_analysis.sif
```

## 方案二：Docker容器

### 优势
- 易于安装和使用
- 跨平台支持
- 丰富的生态系统
- 适合本地开发

### 构建和使用

```bash
# 1. 构建Docker镜像
chmod +x docker_build_and_run.sh
./docker_build_and_run.sh build

# 2. 运行分析
./docker_build_and_run.sh run \
    -l library.xlsx \
    -1 sample_R1.fastq.gz \
    -2 sample_R2.fastq.gz \
    -f "CACCG-GTTTTAGAGCTAGAAATAGC" \
    -o results \
    -s sample_name

# 3. 测试容器
./docker_build_and_run.sh test

# 4. 交互式shell
./docker_build_and_run.sh shell
```

## 容器内包含的软件

### 核心工具
- **Python 2.7** - 主要运行环境
- **FLASH 1.2.11** - 双端序列合并
- **bowtie2 2.4.5** - 序列比对
- **samtools 1.15.1** - BAM文件处理
- **MAGeCK ********* - sgRNA计数分析

### Python包
- **pandas 0.24.2** - 数据处理
- **numpy 1.16.6** - 数值计算
- **matplotlib 2.2.5** - 图表绘制
- **pysam 0.15.4** - SAM/BAM文件处理
- **openpyxl 2.6.4** - Excel文件处理
- **seaborn 0.9.1** - 统计可视化

## 使用示例

### 完整分析流程

```bash
# 1. 准备数据
ls -la
# library.xlsx
# sample_R1.fastq.gz  
# sample_R2.fastq.gz

# 2. 运行分析 (Singularity)
./run_sgrna_analysis.sh \
    --library library.xlsx \
    --r1 sample_R1.fastq.gz \
    --r2 sample_R2.fastq.gz \
    --flanking "CACCG-GTTTTAGAGCTAGAAATAGC" \
    --output analysis_results \
    --sample my_sample

# 3. 检查结果
ls analysis_results/
# lib.csv
# my_sample.extendedFrags.fastq
# my_sample.combined.fq
# my_sample.bam
# my_sample.count.txt
# my_sample.png
# Sample.stat.xlsx
```

### 批量处理

```bash
# 创建批量处理脚本
cat > batch_analysis.sh << 'EOF'
#!/bin/bash

LIBRARY="library.xlsx"
FLANKING="CACCG-GTTTTAGAGCTAGAAATAGC"

for R1 in *_R1.fastq.gz; do
    R2=${R1/_R1/_R2}
    SAMPLE=$(basename $R1 _R1.fastq.gz)
    
    echo "Processing $SAMPLE..."
    
    ./run_sgrna_analysis.sh \
        -l "$LIBRARY" \
        -1 "$R1" \
        -2 "$R2" \
        -f "$FLANKING" \
        -o "results_$SAMPLE" \
        -s "$SAMPLE"
done
EOF

chmod +x batch_analysis.sh
./batch_analysis.sh
```

## 故障排除

### 常见问题

1. **容器构建失败**
   ```bash
   # 检查网络连接
   ping google.com
   
   # 检查磁盘空间
   df -h
   
   # 清理Docker缓存
   docker system prune -a
   ```

2. **权限问题**
   ```bash
   # Singularity需要sudo权限构建
   sudo singularity build sgrna_analysis.sif sgrna_analysis.def
   
   # 或使用fakeroot (如果可用)
   singularity build --fakeroot sgrna_analysis.sif sgrna_analysis.def
   ```

3. **文件路径问题**
   ```bash
   # 使用绝对路径
   realpath library.xlsx
   
   # 确保文件存在
   ls -la library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz
   ```

4. **内存不足**
   ```bash
   # 检查可用内存
   free -h
   
   # 对于大文件，考虑增加swap空间
   sudo fallocate -l 4G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 性能优化

1. **并行处理**
   ```bash
   # 设置并行任务数
   export OMP_NUM_THREADS=4
   
   # 使用GNU parallel处理多个样本
   parallel -j 4 ./run_sgrna_analysis.sh -l library.xlsx -1 {1} -2 {2} -f "CACCG-GTTTTAGAGCTAGAAATAGC" ::: *_R1.fastq.gz :::+ *_R2.fastq.gz
   ```

2. **存储优化**
   ```bash
   # 使用SSD存储临时文件
   export TMPDIR=/fast_storage/tmp
   
   # 压缩中间文件
   gzip *.fastq
   ```

## 容器规格

### 系统要求
- **CPU**: 2+ 核心
- **内存**: 4GB+ (推荐8GB)
- **存储**: 10GB+ 可用空间
- **网络**: 构建时需要互联网连接

### 容器大小
- **Singularity SIF**: ~2-3GB
- **Docker镜像**: ~2-3GB

## 技术支持

### 日志和调试
```bash
# 启用详细日志
export SINGULARITY_DEBUG=1
export DOCKER_BUILDKIT_PROGRESS=plain

# 查看容器内部
singularity shell sgrna_analysis.sif
docker run -it --rm sgrna-analysis:latest /bin/bash
```

### 版本信息
```bash
# 检查容器版本
singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py --version
docker run --rm sgrna-analysis:latest --version
```

## 总结

容器化解决方案完全解决了软件依赖问题，提供了：
- ✅ 一键部署所有依赖
- ✅ 跨平台兼容性
- ✅ 版本一致性
- ✅ 易于分发和部署
- ✅ 隔离的运行环境

选择Singularity用于HPC环境，选择Docker用于本地开发和测试。
