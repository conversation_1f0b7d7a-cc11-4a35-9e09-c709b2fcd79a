#!/bin/bash

# 使用国内镜像源构建容器

set -e

echo "=========================================="
echo "使用国内镜像源构建sgRNA分析容器"
echo "=========================================="

# 检查Singularity
if ! command -v singularity &> /dev/null; then
    echo "错误: Singularity未安装"
    exit 1
fi

echo "✓ Singularity版本: $(singularity --version)"

# 尝试不同的镜像源
MIRRORS=(
    "docker://registry.cn-hangzhou.aliyuncs.com/continuumio/miniconda3:4.10.3"
    "docker://dockerhub.azk8s.cn/continuumio/miniconda3:4.10.3"
    "docker://reg-mirror.qiniu.com/continuumio/miniconda3:4.10.3"
    "docker://continuumio/miniconda3:4.10.3"
)

echo ""
echo "尝试从不同镜像源拉取..."

for mirror in "${MIRRORS[@]}"; do
    echo ""
    echo "尝试镜像源: $mirror"
    
    if timeout 300 singularity pull --name miniconda3_base.sif "$mirror"; then
        echo "✓ 成功从 $mirror 拉取镜像"
        break
    else
        echo "✗ 从 $mirror 拉取失败，尝试下一个..."
        rm -f miniconda3_base.sif
    fi
done

# 检查是否成功
if [ ! -f "miniconda3_base.sif" ]; then
    echo ""
    echo "所有镜像源都失败了，建议使用本地安装方案："
    echo "  ./install_sgrna_environment.sh"
    exit 1
fi

echo ""
echo "=========================================="
echo "镜像拉取成功!"
echo "=========================================="
echo "镜像文件: miniconda3_base.sif"
echo "文件大小: $(du -h miniconda3_base.sif | cut -f1)"

# 创建运行脚本
echo ""
echo "创建运行脚本..."

cat > run_in_container.sh << 'EOF'
#!/bin/bash

# 在容器内运行的脚本
set -e

echo "正在容器内设置环境..."

# 更新conda
conda update -n base -c defaults conda -y

# 创建Python 2.7环境
conda create -n py27 python=2.7 -y

# 激活环境
source activate py27

# 安装Python包
echo "安装Python包..."
conda install -c conda-forge -y \
    pandas=0.24.2 \
    numpy=1.16.6 \
    matplotlib=2.2.5 \
    openpyxl=2.6.4

pip install pysam==0.15.4 seaborn==0.9.1 mageck==*******

# 创建工具目录
mkdir -p /tmp/tools

# 下载FLASH
echo "下载FLASH..."
cd /tmp
wget -q --timeout=30 https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz || {
    echo "FLASH下载失败，使用备用方案"
    exit 1
}
tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
cp FLASH-1.2.11-Linux-x86_64/flash /tmp/tools/
chmod +x /tmp/tools/flash

# 下载bowtie2
echo "下载bowtie2..."
wget -q --timeout=30 https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip || {
    echo "bowtie2下载失败，使用备用方案"
    exit 1
}
unzip -q bowtie2-2.4.5-linux-x86_64.zip
cp bowtie2-2.4.5-linux-x86_64/bowtie2* /tmp/tools/
chmod +x /tmp/tools/bowtie2*

# 下载samtools
echo "下载samtools..."
wget -q --timeout=30 https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2 || {
    echo "samtools下载失败，使用备用方案"
    exit 1
}
tar -xjf samtools-1.15.1.tar.bz2
cd samtools-1.15.1
./configure --prefix=/tmp/tools --disable-bz2 --disable-lzma
make -j2 && make install

# 设置PATH
export PATH=/tmp/tools:/tmp/tools/bin:$PATH

echo "环境设置完成，开始分析..."

# 运行分析
python /mnt/sgrna_analysis_pipeline.py "$@"
EOF

chmod +x run_in_container.sh

# 创建最终运行脚本
cat > run_sgrna_with_mirror.sh << 'EOF'
#!/bin/bash

# 使用镜像容器运行sgRNA分析

set -e

# 检查参数
if [ $# -lt 4 ]; then
    echo "用法: $0 <library.xlsx> <R1.fastq.gz> <R2.fastq.gz> <flanking_sequences> [options]"
    echo ""
    echo "示例:"
    echo "  $0 library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample"
    exit 1
fi

LIBRARY="$1"
R1_FASTQ="$2"
R2_FASTQ="$3"
FLANKING="$4"
shift 4

# 解析其他参数
OUTPUT_DIR="results"
SAMPLE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -o)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -s)
            SAMPLE_NAME="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 检查文件
for file in "$LIBRARY" "$R1_FASTQ" "$R2_FASTQ"; do
    if [ ! -f "$file" ]; then
        echo "错误: 文件不存在: $file"
        exit 1
    fi
done

# 检查容器
if [ ! -f "miniconda3_base.sif" ]; then
    echo "错误: 容器镜像不存在，请先运行构建脚本"
    exit 1
fi

# 提取样本名称
if [ -z "$SAMPLE_NAME" ]; then
    SAMPLE_NAME=$(basename "$R1_FASTQ" | sed 's/_R1.*$//' | sed 's/\.fastq.*$//' | sed 's/\.fq.*$//')
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 获取绝对路径
LIBRARY_ABS=$(realpath "$LIBRARY")
R1_FASTQ_ABS=$(realpath "$R1_FASTQ")
R2_FASTQ_ABS=$(realpath "$R2_FASTQ")
OUTPUT_DIR_ABS=$(realpath "$OUTPUT_DIR")
CURRENT_DIR=$(pwd)

echo "=========================================="
echo "使用容器运行sgRNA分析"
echo "=========================================="
echo "库文件:     $LIBRARY_ABS"
echo "R1文件:     $R1_FASTQ_ABS"
echo "R2文件:     $R2_FASTQ_ABS"
echo "引物序列:   $FLANKING"
echo "输出目录:   $OUTPUT_DIR_ABS"
echo "样本名称:   $SAMPLE_NAME"
echo "=========================================="

# 运行容器
singularity exec \
    --bind "$CURRENT_DIR:/mnt" \
    --bind "$OUTPUT_DIR_ABS:/output" \
    --bind "$(dirname "$LIBRARY_ABS"):/library_dir" \
    --bind "$(dirname "$R1_FASTQ_ABS"):/fastq_dir" \
    --pwd /output \
    miniconda3_base.sif \
    bash /mnt/run_in_container.sh \
    "/library_dir/$(basename "$LIBRARY_ABS")" \
    "/fastq_dir/$(basename "$R1_FASTQ_ABS")" \
    "/fastq_dir/$(basename "$R2_FASTQ_ABS")" \
    "$FLANKING" \
    -o /output \
    -s "$SAMPLE_NAME"

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "分析完成!"
    echo "=========================================="
    echo "输出目录: $OUTPUT_DIR_ABS"
else
    echo ""
    echo "分析失败!"
    exit 1
fi
EOF

chmod +x run_sgrna_with_mirror.sh

echo ""
echo "使用方法:"
echo "  ./run_sgrna_with_mirror.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream' -o results -s sample"
echo ""
