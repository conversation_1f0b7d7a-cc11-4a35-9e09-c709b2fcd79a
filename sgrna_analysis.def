Bootstrap: docker
From: ubuntu:20.04

%labels
    Author CRISPRQC Team
    Version 1.0
    Description sgRNA Analysis Pipeline with all dependencies

%help
    This container includes all tools needed for sgRNA analysis:
    - Python 2.7 with required packages
    - FLASH for read merging
    - bowtie2 for alignment
    - samtools for BAM processing
    - MAGeCK for counting analysis

    Usage:
    singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py [options]

%environment
    export PATH=/opt/conda/bin:/opt/flash:/opt/bowtie2:/opt/samtools/bin:$PATH
    export PYTHONPATH=/opt:$PYTHONPATH
    export DEBIAN_FRONTEND=noninteractive

%post
    # Update system and install basic dependencies
    apt-get update && apt-get install -y \
        wget \
        curl \
        build-essential \
        zlib1g-dev \
        libbz2-dev \
        liblzma-dev \
        libncurses5-dev \
        libcurl4-openssl-dev \
        libssl-dev \
        git \
        unzip \
        ca-certificates \
        && rm -rf /var/lib/apt/lists/*

    # Install Miniconda for Python 2.7
    cd /tmp
    wget --quiet https://repo.anaconda.com/miniconda/Miniconda2-latest-Linux-x86_64.sh -O miniconda.sh
    bash miniconda.sh -b -p /opt/conda
    rm miniconda.sh

    # Add conda to PATH for this session
    export PATH=/opt/conda/bin:$PATH

    # Update conda and install Python packages
    /opt/conda/bin/conda update -n base -c defaults conda -y
    /opt/conda/bin/pip install --no-cache-dir \
        pandas==0.24.2 \
        numpy==1.16.6 \
        matplotlib==2.2.5 \
        pysam==0.15.4 \
        openpyxl==2.6.4 \
        seaborn==0.9.1

    # Install MAGeCK
    /opt/conda/bin/pip install --no-cache-dir mageck==*******

    # Install FLASH
    cd /tmp
    wget --quiet https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
    mkdir -p /opt/flash
    cp FLASH-1.2.11-Linux-x86_64/flash /opt/flash/
    chmod +x /opt/flash/flash
    rm -rf FLASH-1.2.11-Linux-x86_64*

    # Install bowtie2
    cd /tmp
    wget --quiet https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip
    unzip -q bowtie2-2.4.5-linux-x86_64.zip
    mkdir -p /opt/bowtie2
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* /opt/bowtie2/
    chmod +x /opt/bowtie2/bowtie2*
    rm -rf bowtie2-2.4.5-linux-x86_64*

    # Install samtools
    cd /tmp
    wget --quiet https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2
    tar -xjf samtools-1.15.1.tar.bz2
    cd samtools-1.15.1
    ./configure --prefix=/opt/samtools --quiet
    make -j$(nproc) && make install
    cd /tmp
    rm -rf samtools-1.15.1*

    # Clean up
    apt-get clean
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

%files
    sgrna_analysis_pipeline.py /opt/
    sgrna_bowtie2_analysis_mis5.py /opt/

%runscript
    exec python /opt/sgrna_analysis_pipeline.py "$@"
