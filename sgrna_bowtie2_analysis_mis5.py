
# -*- coding: utf-8 -*-
from __future__ import division  # Ensure floating-point division
import matplotlib
matplotlib.use('Agg')  # Use non-interactive Agg backend
import pysam
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import logging
import sys

# Set up logging with UTF-8 encoding for Python 2
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()
if sys.version_info[0] < 3:
    # Redirect logging to handle UTF-8 in Python 2
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logger.handlers = [handler]

def parse_sam_file(sam_file):
    """Parse SAM file and classify reads into 0 to 5 mismatches, indel, or more"""
    # Initialize all categories with zero counts
    results = Counter({
        '0_mismatch': 0,
        '1_mismatch': 0,
        '2_mismatch': 0,
        '3_mismatch': 0,
        '4_mismatch': 0,
        '5_mismatch': 0,
        'indel': 0,
        'more_than_5_mismatch': 0,
        'unmapped': 0
    })
    try:
        samfile = pysam.AlignmentFile(sam_file, "r")
        for read in samfile:
            if read.is_unmapped:
                results['unmapped'] += 1
            else:
                nm = read.get_tag("NM") if read.has_tag("NM") else 0
                cigar = read.cigarstring if read.cigarstring else ""
                
                # Check for indels (I or D in CIGAR string)
                has_indel = 'I' in cigar or 'D' in cigar
                
                if has_indel:
                    results['indel'] += 1
                elif nm <= 5:
                    results['%d_mismatch' % nm] += 1
                else:
                    results['more_than_5_mismatch'] += 1
        samfile.close()
        logging.info(u"Processed %d reads" % sum(results.values()))
        return results
    except Exception, e:
        logging.error(u"Failed to parse SAM file: %s" % str(e))
        raise

def save_results(results, output_file):
    """Save analysis results to CSV"""
    total = sum(results.values())
    # Define desired category order
    category_order = ['0_mismatch', '1_mismatch', '2_mismatch', '3_mismatch', '4_mismatch', '5_mismatch', 'indel', 'more_than_5_mismatch', 'unmapped']
    # Create DataFrame with all categories, even if zero
    data = {
        'Category': category_order,
        'Count': [results[cat] for cat in category_order],
        'Percentage': [100 * results[cat] / total if total > 0 else 0.0 for cat in category_order]
    }
    df = pd.DataFrame(data)
    # Format Percentage to two decimal places when saving to CSV
    df.to_csv(output_file, index=False, float_format='%.2f')
    logging.info(u"Results saved to %s" % output_file)
    return df, total

def plot_results(df, plot_file):
    """Generate bar plot of results"""
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Category', y='Percentage', data=df, order=['0_mismatch', '1_mismatch', '2_mismatch', '3_mismatch', '4_mismatch', '5_mismatch', 'indel', 'more_than_5_mismatch', 'unmapped'])
    plt.title('sgRNA Library Accuracy Analysis')
    plt.ylabel('Percentage (%)')
    plt.xlabel('Category')
    plt.xticks(rotation=45)
    plt.savefig(plot_file, bbox_inches='tight')
    plt.close()
    logging.info(u"Bar plot saved to %s" % plot_file)

def main(sam_file, output_csv, plot_file):
    """Main function"""
    # Parse SAM file
    results = parse_sam_file(sam_file)
    
    # Save results and get total
    df, total = save_results(results, output_csv)
    
    # Plot results
    plot_results(df, plot_file)
    
    # Print summary
    logging.info(u"Analysis completed, results:")
    for category in ['0_mismatch', '1_mismatch', '2_mismatch', '3_mismatch', '4_mismatch', '5_mismatch', 'indel', 'more_than_5_mismatch', 'unmapped']:
        count = results[category]
        percentage = 100 * count / total if total > 0 else 0
        logging.info(u"%s: %d reads (%.2f%%)" % (category, count, percentage))

if __name__ == "__main__":
    sam_file = "PL808-SSC.sam"
    output_csv = "sgrna_analysis_results.csv"
    plot_file = "sgrna_analysis_plot.png"
    main(sam_file, output_csv, plot_file)
