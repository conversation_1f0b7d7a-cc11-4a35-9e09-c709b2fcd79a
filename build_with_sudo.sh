#!/bin/bash

# 使用sudo权限构建SIF容器的简化脚本

set -e

echo "=========================================="
echo "使用sudo构建sgRNA Analysis容器"
echo "=========================================="

# 检查必需文件
required_files=(
    "sgrna_analysis.def"
    "sgrna_analysis_pipeline.py"
    "sgrna_bowtie2_analysis_mis5.py"
)

echo "检查必需文件..."
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file 缺失"
        exit 1
    fi
done

# 检查Singularity
if ! command -v singularity &> /dev/null; then
    echo "错误: Singularity未安装"
    exit 1
fi

echo "✓ Singularity版本: $(singularity --version)"

# 检查sudo权限
echo ""
echo "检查sudo权限..."
if sudo -n true 2>/dev/null; then
    echo "✓ 已有sudo权限"
else
    echo "需要sudo权限，请输入密码:"
    sudo true
fi

# 开始构建
echo ""
echo "=========================================="
echo "开始构建容器 (使用sudo)..."
echo "=========================================="

start_time=$(date +%s)

# 构建容器
sudo singularity build sgrna_analysis.sif sgrna_analysis.def

if [ $? -eq 0 ]; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo "=========================================="
    echo "构建成功!"
    echo "=========================================="
    echo "构建时间: $((duration/60))分$((duration%60))秒"
    echo "容器文件: sgrna_analysis.sif"
    echo "文件大小: $(du -h sgrna_analysis.sif | cut -f1)"
    
    # 修改文件所有者为当前用户
    echo ""
    echo "修改文件所有者..."
    sudo chown $(whoami):$(id -gn) sgrna_analysis.sif
    echo "✓ 文件所有者已修改为: $(whoami)"
    
    # 测试容器
    echo ""
    echo "测试容器..."
    if singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py --help > /dev/null 2>&1; then
        echo "✓ 容器测试通过"
    else
        echo "⚠ 容器测试失败"
    fi
    
    echo ""
    echo "使用方法:"
    echo "  ./run_with_sif.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream'"
    
else
    echo ""
    echo "构建失败!"
    exit 1
fi
