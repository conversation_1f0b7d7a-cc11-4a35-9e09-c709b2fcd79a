#!/bin/bash

# 使用预构建镜像创建sgRNA分析容器
# 这个方法不需要sudo权限

set -e

echo "=========================================="
echo "使用预构建镜像创建sgRNA分析容器"
echo "=========================================="

# 检查Singularity
if ! command -v singularity &> /dev/null; then
    echo "错误: Singularity未安装"
    exit 1
fi

echo "✓ Singularity版本: $(singularity --version)"

# 检查必需文件
required_files=(
    "sgrna_analysis_pipeline.py"
    "sgrna_bowtie2_analysis_mis5.py"
)

echo ""
echo "检查必需文件..."
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file 缺失"
        exit 1
    fi
done

# 方法1: 直接从Docker Hub拉取基础镜像
echo ""
echo "方法1: 拉取预构建的基础镜像..."
echo "这将下载一个包含conda的基础镜像"

if [ ! -f "miniconda3_latest.sif" ]; then
    echo "正在拉取miniconda镜像..."
    singularity pull docker://continuumio/miniconda3:4.10.3
    
    if [ $? -eq 0 ]; then
        echo "✓ 基础镜像下载成功"
        mv miniconda3_4.10.3.sif miniconda3_base.sif
    else
        echo "✗ 基础镜像下载失败"
        exit 1
    fi
else
    echo "✓ 基础镜像已存在"
fi

# 方法2: 创建一个包装脚本，在基础镜像中运行我们的代码
echo ""
echo "创建运行脚本..."

cat > run_in_container.sh << 'EOF'
#!/bin/bash

# 在容器内运行的脚本
set -e

# 安装Python包 (如果需要)
if ! python -c "import pandas" 2>/dev/null; then
    echo "安装Python包..."
    pip install pandas==0.24.2 numpy==1.16.6 matplotlib==2.2.5 openpyxl==2.6.4 pysam==0.15.4 seaborn==0.9.1
fi

# 安装MAGeCK
if ! python -c "import mageck" 2>/dev/null; then
    pip install mageck==*******
fi

# 下载工具 (如果不存在)
mkdir -p /tmp/tools

# FLASH
if [ ! -f "/tmp/tools/flash" ]; then
    echo "下载FLASH..."
    cd /tmp
    wget -q https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
    cp FLASH-1.2.11-Linux-x86_64/flash /tmp/tools/
    chmod +x /tmp/tools/flash
fi

# bowtie2
if [ ! -f "/tmp/tools/bowtie2" ]; then
    echo "下载bowtie2..."
    cd /tmp
    wget -q https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip
    unzip -q bowtie2-2.4.5-linux-x86_64.zip
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* /tmp/tools/
    chmod +x /tmp/tools/bowtie2*
fi

# samtools
if [ ! -f "/tmp/tools/samtools" ]; then
    echo "下载samtools..."
    cd /tmp
    wget -q https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2
    tar -xjf samtools-1.15.1.tar.bz2
    cd samtools-1.15.1
    make configure-htslib
    ./configure --prefix=/tmp/tools --disable-bz2 --disable-lzma
    make -j2 && make install
fi

# 设置PATH
export PATH=/tmp/tools:/tmp/tools/bin:$PATH

# 运行分析
python /mnt/sgrna_analysis_pipeline.py "$@"
EOF

chmod +x run_in_container.sh

# 创建最终的运行脚本
cat > run_sgrna_with_prebuilt.sh << 'EOF'
#!/bin/bash

# 使用预构建容器运行sgRNA分析

set -e

# 检查参数
if [ $# -lt 4 ]; then
    echo "用法: $0 <library.xlsx> <R1.fastq.gz> <R2.fastq.gz> <flanking_sequences> [options]"
    echo ""
    echo "示例:"
    echo "  $0 library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample"
    exit 1
fi

LIBRARY="$1"
R1_FASTQ="$2"
R2_FASTQ="$3"
FLANKING="$4"
shift 4

# 解析其他参数
OUTPUT_DIR="results"
SAMPLE_NAME=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -o)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -s)
            SAMPLE_NAME="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 检查文件
for file in "$LIBRARY" "$R1_FASTQ" "$R2_FASTQ"; do
    if [ ! -f "$file" ]; then
        echo "错误: 文件不存在: $file"
        exit 1
    fi
done

# 检查容器
if [ ! -f "miniconda3_base.sif" ]; then
    echo "错误: 基础容器不存在，请先运行构建脚本"
    exit 1
fi

# 提取样本名称
if [ -z "$SAMPLE_NAME" ]; then
    SAMPLE_NAME=$(basename "$R1_FASTQ" | sed 's/_R1.*$//' | sed 's/\.fastq.*$//' | sed 's/\.fq.*$//')
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 获取绝对路径
LIBRARY_ABS=$(realpath "$LIBRARY")
R1_FASTQ_ABS=$(realpath "$R1_FASTQ")
R2_FASTQ_ABS=$(realpath "$R2_FASTQ")
OUTPUT_DIR_ABS=$(realpath "$OUTPUT_DIR")
CURRENT_DIR=$(pwd)

echo "=========================================="
echo "使用预构建容器运行sgRNA分析"
echo "=========================================="
echo "库文件:     $LIBRARY_ABS"
echo "R1文件:     $R1_FASTQ_ABS"
echo "R2文件:     $R2_FASTQ_ABS"
echo "引物序列:   $FLANKING"
echo "输出目录:   $OUTPUT_DIR_ABS"
echo "样本名称:   $SAMPLE_NAME"
echo "=========================================="

# 运行容器
singularity exec \
    --bind "$CURRENT_DIR:/mnt" \
    --bind "$OUTPUT_DIR_ABS:/output" \
    --bind "$(dirname "$LIBRARY_ABS"):/library_dir" \
    --bind "$(dirname "$R1_FASTQ_ABS"):/fastq_dir" \
    --pwd /output \
    miniconda3_base.sif \
    bash /mnt/run_in_container.sh \
    "/library_dir/$(basename "$LIBRARY_ABS")" \
    "/fastq_dir/$(basename "$R1_FASTQ_ABS")" \
    "/fastq_dir/$(basename "$R2_FASTQ_ABS")" \
    "$FLANKING" \
    -o /output \
    -s "$SAMPLE_NAME"

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "分析完成!"
    echo "=========================================="
    echo "输出目录: $OUTPUT_DIR_ABS"
    
    # 显示生成的文件
    echo ""
    echo "生成的文件:"
    ls -la "$OUTPUT_DIR_ABS"
else
    echo ""
    echo "分析失败!"
    exit 1
fi
EOF

chmod +x run_sgrna_with_prebuilt.sh

echo ""
echo "=========================================="
echo "预构建容器设置完成!"
echo "=========================================="
echo ""
echo "生成的文件:"
echo "  - miniconda3_base.sif (基础容器镜像)"
echo "  - run_in_container.sh (容器内运行脚本)"
echo "  - run_sgrna_with_prebuilt.sh (主运行脚本)"
echo ""
echo "使用方法:"
echo "  ./run_sgrna_with_prebuilt.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream' -o results -s sample"
echo ""
echo "示例:"
echo "  ./run_sgrna_with_prebuilt.sh sample_library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample_name"
echo ""
