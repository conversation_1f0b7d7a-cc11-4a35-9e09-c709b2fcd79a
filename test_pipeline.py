#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
Test script for sgRNA Analysis Pipeline
"""

import os
import sys
import tempfile
import pandas as pd
from sgrna_analysis_pipeline import (
    reverse_complement, 
    process_excel_to_csv, 
    extract_sgrna_from_read,
    process_fastq_file,
    write_fastq_records
)

def test_reverse_complement():
    """Test reverse complement function"""
    print("Testing reverse complement...")
    
    test_cases = [
        ("ATCG", "CGAT"),
        ("AAAA", "TTTT"),
        ("GCGC", "GCGC"),
        ("ATCGATCG", "CGATCGAT")
    ]
    
    for seq, expected in test_cases:
        result = reverse_complement(seq)
        assert result == expected, "Failed for %s: got %s, expected %s" % (seq, result, expected)
        print("  %s -> %s ✓" % (seq, result))
    
    print("Reverse complement tests passed!\n")

def test_sgrna_extraction():
    """Test sgRNA extraction function"""
    print("Testing sgRNA extraction...")
    
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    min_len = 20
    max_len = 20
    
    # Test forward orientation
    sgrna_seq = "ATCGATCGATCGATCGATCG"
    read_seq = upstream + sgrna_seq + downstream + "EXTRA"
    read_qual = "I" * len(read_seq)
    
    extracted_seq, extracted_qual = extract_sgrna_from_read(
        read_seq, read_qual, upstream, downstream, min_len, max_len
    )
    
    assert extracted_seq == sgrna_seq, "Forward extraction failed: got %s" % extracted_seq
    assert len(extracted_qual) == len(sgrna_seq), "Quality length mismatch"
    print("  Forward orientation extraction ✓")
    
    # Test reverse complement orientation
    upstream_rc = reverse_complement(upstream)
    downstream_rc = reverse_complement(downstream)
    sgrna_rc = reverse_complement(sgrna_seq)
    
    read_seq_rc = downstream_rc + sgrna_rc + upstream_rc + "EXTRA"
    read_qual_rc = "I" * len(read_seq_rc)
    
    extracted_seq_rc, extracted_qual_rc = extract_sgrna_from_read(
        read_seq_rc, read_qual_rc, upstream, downstream, min_len, max_len
    )
    
    assert extracted_seq_rc == sgrna_seq, "Reverse complement extraction failed: got %s" % extracted_seq_rc
    print("  Reverse complement orientation extraction ✓")
    
    # Test no match
    no_match_seq = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
    no_match_qual = "I" * len(no_match_seq)
    
    extracted_none, qual_none = extract_sgrna_from_read(
        no_match_seq, no_match_qual, upstream, downstream, min_len, max_len
    )
    
    assert extracted_none is None, "Should return None for no match"
    assert qual_none is None, "Should return None for no match"
    print("  No match case ✓")
    
    print("sgRNA extraction tests passed!\n")

def create_test_excel():
    """Create a test Excel file"""
    print("Creating test Excel file...")
    
    data = {
        'sgRNA_ID': ['gRNA_1', 'gRNA_2', 'gRNA_3', 'gRNA_4', 'gRNA_5'],
        'sgRNA_sequence': [
            'ATCGATCGATCGATCGATCG',
            'GCGCGCGCGCGCGCGCGCGC',
            'ATATATATATATATATATA',
            'CGCGCGCGCGCGCGCGCGCG',
            'TATATATATATATATATATA'
        ],
        'Gene': ['Gene1', 'Gene2', 'Gene3', 'Gene4', 'Gene5'],
        'Other_column': ['A', 'B', 'C', 'D', 'E']
    }
    
    df = pd.DataFrame(data)
    excel_file = 'test_library.xlsx'
    df.to_excel(excel_file, index=False)
    
    print("  Created test Excel file: %s" % excel_file)
    return excel_file

def test_excel_to_csv():
    """Test Excel to CSV conversion"""
    print("Testing Excel to CSV conversion...")
    
    excel_file = create_test_excel()
    csv_file = 'test_lib.csv'
    
    min_len, max_len = process_excel_to_csv(excel_file, csv_file)
    
    # Check if CSV was created
    assert os.path.exists(csv_file), "CSV file was not created"
    
    # Check content
    df = pd.read_csv(csv_file)
    assert len(df) == 5, "Wrong number of rows in CSV"
    assert list(df.columns) == ['sgRNA_ID', 'sgRNA_sequence'], "Wrong columns in CSV"
    assert min_len == 20, "Wrong minimum length: %d" % min_len
    assert max_len == 20, "Wrong maximum length: %d" % max_len
    
    print("  CSV conversion ✓")
    print("  Length range: %d-%d ✓" % (min_len, max_len))
    
    # Cleanup
    os.remove(excel_file)
    os.remove(csv_file)
    
    print("Excel to CSV tests passed!\n")

def create_test_fastq():
    """Create a test FASTQ file"""
    print("Creating test FASTQ file...")
    
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    
    # Create test reads with sgRNA sequences
    reads = [
        ("read1", upstream + "ATCGATCGATCGATCGATCG" + downstream + "EXTRA"),
        ("read2", "EXTRA" + upstream + "GCGCGCGCGCGCGCGCGCGC" + downstream),
        ("read3", "NOMATCH" + "AAAAAAAAAAAAAAAAAAAA" + "NOMATCH"),
        ("read4", reverse_complement(downstream) + "ATATATATATATATATATA" + reverse_complement(upstream))
    ]
    
    fastq_file = 'test_reads.fastq'
    with open(fastq_file, 'w') as f:
        for i, (read_id, seq) in enumerate(reads):
            qual = "I" * len(seq)
            f.write("@%s\n%s\n+\n%s\n" % (read_id, seq, qual))
    
    print("  Created test FASTQ file: %s" % fastq_file)
    return fastq_file

def test_fastq_processing():
    """Test FASTQ file processing"""
    print("Testing FASTQ processing...")
    
    fastq_file = create_test_fastq()
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    min_len = 20
    max_len = 20
    
    extracted_reads = process_fastq_file(fastq_file, upstream, downstream, min_len, max_len)
    
    # Should extract 3 out of 4 reads (read3 has no match)
    assert len(extracted_reads) == 3, "Expected 3 extracted reads, got %d" % len(extracted_reads)
    
    # Check extracted sequences
    expected_sequences = [
        "ATCGATCGATCGATCGATCG",
        "GCGCGCGCGCGCGCGCGCGC", 
        "ATATATATATATATATATA"
    ]
    
    extracted_sequences = [seq for _, seq, _ in extracted_reads]
    for expected in expected_sequences:
        assert expected in extracted_sequences, "Missing expected sequence: %s" % expected
    
    print("  Extracted %d reads ✓" % len(extracted_reads))
    
    # Test writing extracted reads
    output_file = 'test_extracted.fastq'
    write_fastq_records(extracted_reads, output_file)
    
    assert os.path.exists(output_file), "Output FASTQ file was not created"
    
    # Count lines in output file (should be 4 * number of extracted reads)
    with open(output_file, 'r') as f:
        lines = f.readlines()
    
    assert len(lines) == 4 * len(extracted_reads), "Wrong number of lines in output FASTQ"
    
    print("  FASTQ writing ✓")
    
    # Cleanup
    os.remove(fastq_file)
    os.remove(output_file)
    
    print("FASTQ processing tests passed!\n")

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("Running sgRNA Analysis Pipeline Tests")
    print("=" * 50)
    
    try:
        test_reverse_complement()
        test_sgrna_extraction()
        test_excel_to_csv()
        test_fastq_processing()
        
        print("=" * 50)
        print("All tests passed successfully! ✓")
        print("=" * 50)
        
    except Exception as e:
        print("=" * 50)
        print("Test failed with error: %s" % str(e))
        print("=" * 50)
        sys.exit(1)

if __name__ == '__main__':
    run_all_tests()
