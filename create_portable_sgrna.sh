#!/bin/bash

# 创建便携式sgRNA分析包
# 将所有工具和脚本打包到一个目录中

set -e

PACKAGE_NAME="sgrna_analysis_portable"
PACKAGE_DIR="$PWD/$PACKAGE_NAME"

echo "=========================================="
echo "创建便携式sgRNA分析包"
echo "=========================================="
echo "包目录: $PACKAGE_DIR"
echo ""

# 创建包目录结构
mkdir -p "$PACKAGE_DIR"/{bin,lib,scripts,tools,data}

echo "1. 复制Python脚本..."
if [ -f "sgrna_analysis_pipeline.py" ]; then
    cp sgrna_analysis_pipeline.py "$PACKAGE_DIR/scripts/"
    echo "  ✓ sgrna_analysis_pipeline.py"
else
    echo "  ✗ sgrna_analysis_pipeline.py 不存在"
fi

if [ -f "sgrna_bowtie2_analysis_mis5.py" ]; then
    cp sgrna_bowtie2_analysis_mis5.py "$PACKAGE_DIR/scripts/"
    echo "  ✓ sgrna_bowtie2_analysis_mis5.py"
else
    echo "  ✗ sgrna_bowtie2_analysis_mis5.py 不存在"
fi

if [ -f "sgrna_analysis_standalone.py" ]; then
    cp sgrna_analysis_standalone.py "$PACKAGE_DIR/scripts/"
    echo "  ✓ sgrna_analysis_standalone.py (备用)"
fi

echo ""
echo "2. 创建环境检测脚本..."
cat > "$PACKAGE_DIR/check_environment.py" << 'EOF'
#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
检查sgRNA分析环境
"""

import sys
import subprocess

def check_python_packages():
    """检查Python包"""
    packages = [
        'pandas', 'numpy', 'matplotlib', 'pysam', 
        'openpyxl', 'seaborn'
    ]
    
    missing = []
    for pkg in packages:
        try:
            __import__(pkg)
            print("✓ %s" % pkg)
        except ImportError:
            print("✗ %s (缺失)" % pkg)
            missing.append(pkg)
    
    return missing

def check_external_tools():
    """检查外部工具"""
    tools = ['flash', 'bowtie2', 'samtools', 'mageck']
    
    missing = []
    for tool in tools:
        try:
            subprocess.check_output([tool, '--help'], stderr=subprocess.STDOUT)
            print("✓ %s" % tool)
        except (subprocess.CalledProcessError, OSError):
            print("✗ %s (缺失)" % tool)
            missing.append(tool)
    
    return missing

def main():
    print("========================================")
    print("sgRNA分析环境检查")
    print("========================================")
    
    print("\nPython版本:")
    print("  %s" % sys.version)
    
    print("\nPython包检查:")
    missing_packages = check_python_packages()
    
    print("\n外部工具检查:")
    missing_tools = check_external_tools()
    
    print("\n========================================")
    if not missing_packages and not missing_tools:
        print("✓ 环境检查通过，可以运行分析")
        return 0
    else:
        print("✗ 环境不完整")
        if missing_packages:
            print("缺失Python包: %s" % ', '.join(missing_packages))
        if missing_tools:
            print("缺失外部工具: %s" % ', '.join(missing_tools))
        return 1

if __name__ == '__main__':
    sys.exit(main())
EOF

chmod +x "$PACKAGE_DIR/check_environment.py"

echo ""
echo "3. 创建主运行脚本..."
cat > "$PACKAGE_DIR/run_analysis.sh" << 'EOF'
#!/bin/bash

# sgRNA分析主运行脚本

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 设置环境变量
export PATH="$SCRIPT_DIR/bin:$SCRIPT_DIR/tools:$PATH"
export PYTHONPATH="$SCRIPT_DIR/scripts:$PYTHONPATH"

echo "=========================================="
echo "sgRNA分析流程"
echo "=========================================="
echo "工作目录: $SCRIPT_DIR"
echo ""

# 检查环境
echo "检查环境..."
python "$SCRIPT_DIR/check_environment.py"

if [ $? -ne 0 ]; then
    echo ""
    echo "环境检查失败，尝试使用独立版本..."
    
    if [ -f "$SCRIPT_DIR/scripts/sgrna_analysis_standalone.py" ]; then
        echo "使用独立Python脚本运行..."
        python "$SCRIPT_DIR/scripts/sgrna_analysis_standalone.py" "$@"
    else
        echo "错误: 无法找到可用的分析脚本"
        exit 1
    fi
else
    echo ""
    echo "环境正常，使用完整版本..."
    python "$SCRIPT_DIR/scripts/sgrna_analysis_pipeline.py" "$@"
fi
EOF

chmod +x "$PACKAGE_DIR/run_analysis.sh"

echo ""
echo "4. 创建安装脚本..."
cat > "$PACKAGE_DIR/install_dependencies.sh" << 'EOF'
#!/bin/bash

# 安装依赖脚本

echo "=========================================="
echo "安装sgRNA分析依赖"
echo "=========================================="

# 检查Python
if ! command -v python2 &> /dev/null && ! command -v python &> /dev/null; then
    echo "错误: 未找到Python"
    exit 1
fi

PYTHON_CMD="python"
if command -v python2 &> /dev/null; then
    PYTHON_CMD="python2"
fi

echo "使用Python: $($PYTHON_CMD --version)"

# 检查pip
if ! command -v pip &> /dev/null; then
    echo "安装pip..."
    curl https://bootstrap.pypa.io/pip/2.7/get-pip.py -o get-pip.py
    $PYTHON_CMD get-pip.py --user
    rm get-pip.py
fi

echo ""
echo "安装Python包..."
pip install --user \
    pandas==0.24.2 \
    numpy==1.16.6 \
    matplotlib==2.2.5 \
    pysam==0.15.4 \
    openpyxl==2.6.4 \
    seaborn==0.9.1 \
    mageck==*******

echo ""
echo "下载外部工具..."

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
mkdir -p "$SCRIPT_DIR/tools"

# 下载FLASH
if [ ! -f "$SCRIPT_DIR/tools/flash" ]; then
    echo "下载FLASH..."
    cd /tmp
    wget -q https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz
    cp FLASH-1.2.11-Linux-x86_64/flash "$SCRIPT_DIR/tools/"
    chmod +x "$SCRIPT_DIR/tools/flash"
    rm -rf FLASH-1.2.11-Linux-x86_64*
fi

# 下载bowtie2
if [ ! -f "$SCRIPT_DIR/tools/bowtie2" ]; then
    echo "下载bowtie2..."
    cd /tmp
    wget -q https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip
    unzip -q bowtie2-2.4.5-linux-x86_64.zip
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* "$SCRIPT_DIR/tools/"
    chmod +x "$SCRIPT_DIR/tools/bowtie2"*
    rm -rf bowtie2-2.4.5-linux-x86_64*
fi

# 下载samtools
if [ ! -f "$SCRIPT_DIR/tools/samtools" ]; then
    echo "下载并编译samtools..."
    cd /tmp
    wget -q https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2
    tar -xjf samtools-1.15.1.tar.bz2
    cd samtools-1.15.1
    ./configure --prefix="$SCRIPT_DIR" --disable-bz2 --disable-lzma
    make -j2 && make install
    rm -rf /tmp/samtools-1.15.1*
fi

echo ""
echo "依赖安装完成!"
echo "运行检查: $SCRIPT_DIR/check_environment.py"
EOF

chmod +x "$PACKAGE_DIR/install_dependencies.sh"

echo ""
echo "5. 创建使用说明..."
cat > "$PACKAGE_DIR/README.txt" << 'EOF'
sgRNA分析便携式包
================

这个包包含了sgRNA分析所需的所有脚本和工具。

目录结构:
- scripts/          Python分析脚本
- tools/            外部工具 (FLASH, bowtie2, samtools)
- bin/              二进制文件
- data/             示例数据 (如果有)
- check_environment.py    环境检查脚本
- install_dependencies.sh 依赖安装脚本
- run_analysis.sh         主运行脚本

使用方法:

1. 首次使用，安装依赖:
   ./install_dependencies.sh

2. 检查环境:
   python check_environment.py

3. 运行分析:
   ./run_analysis.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream' -o results -s sample

示例:
   ./run_analysis.sh sample_library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample_name

输出文件:
- Sample.stat.xlsx    最终统计报告
- sample.png          累积分布图
- sample.count.txt    计数结果
- lib.csv             转换后的库文件

注意事项:
- 需要Linux环境
- 需要Python 2.7
- 需要网络连接下载工具 (首次安装)
- 建议至少4GB内存和10GB磁盘空间
EOF

echo ""
echo "6. 创建快速启动脚本..."
cat > "$PACKAGE_DIR/quick_start.sh" << 'EOF'
#!/bin/bash

echo "=========================================="
echo "sgRNA分析快速启动"
echo "=========================================="

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "1. 检查环境..."
python "$SCRIPT_DIR/check_environment.py"

if [ $? -ne 0 ]; then
    echo ""
    echo "环境不完整，是否安装依赖? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        "$SCRIPT_DIR/install_dependencies.sh"
    else
        echo "请手动安装依赖后再运行"
        exit 1
    fi
fi

echo ""
echo "2. 环境准备完成!"
echo ""
echo "使用方法:"
echo "  $SCRIPT_DIR/run_analysis.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream'"
echo ""
echo "示例:"
echo "  $SCRIPT_DIR/run_analysis.sh sample_library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample"
EOF

chmod +x "$PACKAGE_DIR/quick_start.sh"

echo ""
echo "=========================================="
echo "便携式包创建完成!"
echo "=========================================="
echo ""
echo "包目录: $PACKAGE_DIR"
echo "包大小: $(du -sh $PACKAGE_DIR | cut -f1)"
echo ""
echo "使用方法:"
echo ""
echo "1. 快速启动:"
echo "   cd $PACKAGE_DIR"
echo "   ./quick_start.sh"
echo ""
echo "2. 或者分步执行:"
echo "   cd $PACKAGE_DIR"
echo "   ./install_dependencies.sh    # 安装依赖"
echo "   python check_environment.py  # 检查环境"
echo "   ./run_analysis.sh [参数]     # 运行分析"
echo ""
echo "3. 打包分发:"
echo "   tar -czf sgrna_analysis_portable.tar.gz $PACKAGE_NAME"
echo ""
echo "包内容:"
ls -la "$PACKAGE_DIR"
echo ""
