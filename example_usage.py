#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
Example usage of sgRNA Analysis Pipeline
This script demonstrates how to use the pipeline with sample data
"""

import os
import sys
import pandas as pd
import gzip

def create_sample_library():
    """Create a sample sgRNA library Excel file"""
    print("Creating sample sgRNA library...")
    
    # Sample sgRNA library data
    data = {
        'sgRNA_ID': [
            'gRNA_1', 'gRNA_2', 'gRNA_3', 'gRNA_4', 'gRNA_5',
            'gRNA_6', 'gRNA_7', 'gRNA_8', 'gRNA_9', 'gRNA_10'
        ],
        'sgRNA_sequence': [
            'ATCGATCGATCGATCGATCG',  # 20 bp
            'GCGCGCGCGCGCGCGCGCGC',  # 20 bp
            'ATATATATATATATATATAT',  # 20 bp
            'CGCGCGCGCGCGCGCGCGCG',  # 20 bp
            'TATATATATATATATATATA',  # 20 bp
            'AAAAAAAAAAAAAAAAAAAT',  # 20 bp
            'TTTTTTTTTTTTTTTTTTTT',  # 20 bp
            'GGGGGGGGGGGGGGGGGGGG',  # 20 bp
            'CCCCCCCCCCCCCCCCCCCC',  # 20 bp
            'ACGTACGTACGTACGTACGT'   # 20 bp
        ],
        'Gene': [
            'Gene1', 'Gene2', 'Gene3', 'Gene4', 'Gene5',
            'Gene6', 'Gene7', 'Gene8', 'Gene9', 'Gene10'
        ],
        'Description': [
            'Target gene 1', 'Target gene 2', 'Target gene 3', 'Target gene 4', 'Target gene 5',
            'Target gene 6', 'Target gene 7', 'Target gene 8', 'Target gene 9', 'Target gene 10'
        ]
    }
    
    df = pd.DataFrame(data)
    excel_file = 'sample_library.xlsx'
    df.to_excel(excel_file, index=False)
    
    print("  Created sample library: %s" % excel_file)
    print("  Number of sgRNAs: %d" % len(df))
    print("  sgRNA length: %d bp" % len(df['sgRNA_sequence'].iloc[0]))
    
    return excel_file

def create_sample_fastq_files():
    """Create sample FASTQ files for testing"""
    print("Creating sample FASTQ files...")
    
    # Define flanking sequences
    upstream = "CACCG"
    downstream = "GTTTTAGAGCTAGAAATAGC"
    
    # Sample sgRNA sequences from the library
    sgrna_sequences = [
        'ATCGATCGATCGATCGATCG',
        'GCGCGCGCGCGCGCGCGCGC',
        'ATATATATATATATATATAT',
        'CGCGCGCGCGCGCGCGCGCG',
        'TATATATATATATATATATA'
    ]
    
    # Create R1 and R2 reads
    r1_reads = []
    r2_reads = []
    
    read_id = 1
    
    # Create reads that will merge successfully
    for i, sgrna in enumerate(sgrna_sequences):
        for copy in range(100):  # 100 copies of each sgRNA
            # Forward orientation
            full_construct = upstream + sgrna + downstream
            
            # Simulate PE150 reads with overlap
            r1_seq = full_construct[:75]  # First 75 bp
            r2_seq = full_construct[25:]  # Last part with 50 bp overlap
            r2_seq = reverse_complement(r2_seq)  # R2 is reverse complement
            
            r1_qual = "I" * len(r1_seq)
            r2_qual = "I" * len(r2_seq)
            
            r1_reads.append("@read_%d/1\n%s\n+\n%s\n" % (read_id, r1_seq, r1_qual))
            r2_reads.append("@read_%d/2\n%s\n+\n%s\n" % (read_id, r2_seq, r2_qual))
            
            read_id += 1
    
    # Create some reads that won't merge (no overlap)
    for i, sgrna in enumerate(sgrna_sequences[:3]):
        for copy in range(50):  # 50 copies each
            full_construct = upstream + sgrna + downstream
            
            # No overlap between R1 and R2
            r1_seq = full_construct[:50]
            r2_seq = full_construct[100:]
            r2_seq = reverse_complement(r2_seq)
            
            r1_qual = "I" * len(r1_seq)
            r2_qual = "I" * len(r2_seq)
            
            r1_reads.append("@read_%d/1\n%s\n+\n%s\n" % (read_id, r1_seq, r1_qual))
            r2_reads.append("@read_%d/2\n%s\n+\n%s\n" % (read_id, r2_seq, r2_qual))
            
            read_id += 1
    
    # Write R1 file
    r1_file = 'sample_R1.fastq.gz'
    with gzip.open(r1_file, 'wb') as f:
        for read in r1_reads:
            f.write(read.encode('utf-8'))
    
    # Write R2 file
    r2_file = 'sample_R2.fastq.gz'
    with gzip.open(r2_file, 'wb') as f:
        for read in r2_reads:
            f.write(read.encode('utf-8'))
    
    print("  Created R1 file: %s (%d reads)" % (r1_file, len(r1_reads)))
    print("  Created R2 file: %s (%d reads)" % (r2_file, len(r2_reads)))
    
    return r1_file, r2_file

def reverse_complement(seq):
    """Return reverse complement of DNA sequence"""
    complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
    return ''.join(complement.get(base, base) for base in reversed(seq.upper()))

def run_example():
    """Run the example pipeline"""
    print("=" * 60)
    print("sgRNA Analysis Pipeline - Example Usage")
    print("=" * 60)
    
    # Create sample data
    excel_file = create_sample_library()
    r1_file, r2_file = create_sample_fastq_files()
    
    # Define parameters
    flanking_sequences = "CACCG-GTTTTAGAGCTAGAAATAGC"
    output_dir = "example_output"
    sample_name = "sample_test"
    
    print("\nPipeline parameters:")
    print("  Library file: %s" % excel_file)
    print("  R1 FASTQ: %s" % r1_file)
    print("  R2 FASTQ: %s" % r2_file)
    print("  Flanking sequences: %s" % flanking_sequences)
    print("  Output directory: %s" % output_dir)
    print("  Sample name: %s" % sample_name)
    
    # Create the command
    cmd = [
        'python', 'sgrna_analysis_pipeline.py',
        excel_file,
        r1_file,
        r2_file,
        flanking_sequences,
        '-o', output_dir,
        '-s', sample_name
    ]
    
    print("\nCommand to run:")
    print("  %s" % ' '.join(cmd))
    
    print("\nTo run this example:")
    print("1. Make sure all required software is installed:")
    print("   - FLASH, bowtie2, samtools, MAGeCK")
    print("2. Install Python dependencies:")
    print("   - pip install pandas numpy matplotlib pysam openpyxl")
    print("3. Run the command above")
    
    print("\nExpected outputs in '%s' directory:" % output_dir)
    print("  - lib.csv (converted library)")
    print("  - sample_test.extendedFrags.fastq (merged reads)")
    print("  - sample_test.combined.fq (extracted sgRNAs from merged reads)")
    print("  - sample_test.nocombined.fq (extracted sgRNAs from unmerged reads)")
    print("  - sample_test.fq (all extracted sgRNAs)")
    print("  - reference_sgrna.fasta (reference sequences)")
    print("  - sample_test.bam (alignment results)")
    print("  - sample_test.count.txt (MAGeCK count results)")
    print("  - sample_test.log (MAGeCK log)")
    print("  - sample_test.png (cumulative distribution plot)")
    print("  - Sample.stat.xls (final summary report)")
    
    print("\n" + "=" * 60)
    print("Example setup completed!")
    print("Run the pipeline command above to process the sample data.")
    print("=" * 60)

if __name__ == '__main__':
    run_example()
