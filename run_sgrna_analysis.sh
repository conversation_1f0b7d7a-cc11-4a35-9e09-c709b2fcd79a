#!/bin/bash

# sgRNA Analysis Pipeline Container Runner
# This script runs the sgRNA analysis using the Singularity container

set -e

# Default values
CONTAINER="sgrna_analysis.sif"
OUTPUT_DIR="results"
SAMPLE_NAME=""

# Function to show usage
show_usage() {
    echo "sgRNA Analysis Pipeline Container Runner"
    echo ""
    echo "Usage:"
    echo "  $0 -l LIBRARY -1 R1_FASTQ -2 R2_FASTQ -f FLANKING [OPTIONS]"
    echo ""
    echo "Required arguments:"
    echo "  -l, --library LIBRARY     sgRNA library Excel file"
    echo "  -1, --r1 R1_FASTQ         R1 FASTQ file (can be .gz)"
    echo "  -2, --r2 R2_FASTQ         R2 FASTQ file (can be .gz)"
    echo "  -f, --flanking FLANKING   Flanking sequences (upstream-downstream)"
    echo ""
    echo "Optional arguments:"
    echo "  -o, --output OUTPUT_DIR   Output directory (default: results)"
    echo "  -s, --sample SAMPLE_NAME  Sample name (default: from R1 filename)"
    echo "  -c, --container CONTAINER Container file (default: sgrna_analysis.sif)"
    echo "  -h, --help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -l library.xlsx -1 sample_R1.fastq.gz -2 sample_R2.fastq.gz \\"
    echo "     -f 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s my_sample"
    echo ""
    echo "  $0 --library lib.xlsx --r1 R1.fq.gz --r2 R2.fq.gz \\"
    echo "     --flanking 'UPSTREAM-DOWNSTREAM' --output analysis_results"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--library)
            LIBRARY="$2"
            shift 2
            ;;
        -1|--r1)
            R1_FASTQ="$2"
            shift 2
            ;;
        -2|--r2)
            R2_FASTQ="$2"
            shift 2
            ;;
        -f|--flanking)
            FLANKING="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -s|--sample)
            SAMPLE_NAME="$2"
            shift 2
            ;;
        -c|--container)
            CONTAINER="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check required arguments
if [ -z "$LIBRARY" ] || [ -z "$R1_FASTQ" ] || [ -z "$R2_FASTQ" ] || [ -z "$FLANKING" ]; then
    echo "Error: Missing required arguments"
    echo ""
    show_usage
    exit 1
fi

# Check if files exist
if [ ! -f "$LIBRARY" ]; then
    echo "Error: Library file not found: $LIBRARY"
    exit 1
fi

if [ ! -f "$R1_FASTQ" ]; then
    echo "Error: R1 FASTQ file not found: $R1_FASTQ"
    exit 1
fi

if [ ! -f "$R2_FASTQ" ]; then
    echo "Error: R2 FASTQ file not found: $R2_FASTQ"
    exit 1
fi

if [ ! -f "$CONTAINER" ]; then
    echo "Error: Container file not found: $CONTAINER"
    echo "Please build the container first using: bash build_container.sh"
    exit 1
fi

# Extract sample name from R1 filename if not provided
if [ -z "$SAMPLE_NAME" ]; then
    SAMPLE_NAME=$(basename "$R1_FASTQ" | sed 's/_R1.*$//' | sed 's/\.fastq.*$//' | sed 's/\.fq.*$//')
    echo "Auto-detected sample name: $SAMPLE_NAME"
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Get absolute paths
LIBRARY_ABS=$(realpath "$LIBRARY")
R1_FASTQ_ABS=$(realpath "$R1_FASTQ")
R2_FASTQ_ABS=$(realpath "$R2_FASTQ")
OUTPUT_DIR_ABS=$(realpath "$OUTPUT_DIR")
CONTAINER_ABS=$(realpath "$CONTAINER")

echo "=========================================="
echo "sgRNA Analysis Pipeline"
echo "=========================================="
echo "Library:     $LIBRARY_ABS"
echo "R1 FASTQ:    $R1_FASTQ_ABS"
echo "R2 FASTQ:    $R2_FASTQ_ABS"
echo "Flanking:    $FLANKING"
echo "Output:      $OUTPUT_DIR_ABS"
echo "Sample:      $SAMPLE_NAME"
echo "Container:   $CONTAINER_ABS"
echo "=========================================="

# Run the analysis
echo "Starting analysis..."
echo ""

singularity exec \
    --bind "$OUTPUT_DIR_ABS:/output" \
    --bind "$(dirname "$LIBRARY_ABS"):/library_dir" \
    --bind "$(dirname "$R1_FASTQ_ABS"):/fastq_dir" \
    --pwd /output \
    "$CONTAINER_ABS" \
    python /opt/sgrna_analysis_pipeline.py \
    "/library_dir/$(basename "$LIBRARY_ABS")" \
    "/fastq_dir/$(basename "$R1_FASTQ_ABS")" \
    "/fastq_dir/$(basename "$R2_FASTQ_ABS")" \
    "$FLANKING" \
    -o /output \
    -s "$SAMPLE_NAME"

if [ $? -eq 0 ]; then
    echo ""
    echo "=========================================="
    echo "Analysis completed successfully!"
    echo "=========================================="
    echo ""
    echo "Output files in: $OUTPUT_DIR_ABS"
    echo ""
    if [ -f "$OUTPUT_DIR_ABS/Sample.stat.xlsx" ]; then
        echo "Final report: $OUTPUT_DIR_ABS/Sample.stat.xlsx"
    fi
    if [ -f "$OUTPUT_DIR_ABS/${SAMPLE_NAME}.png" ]; then
        echo "Distribution plot: $OUTPUT_DIR_ABS/${SAMPLE_NAME}.png"
    fi
    echo ""
else
    echo ""
    echo "=========================================="
    echo "Analysis failed!"
    echo "=========================================="
    echo "Please check the error messages above"
    exit 1
fi
