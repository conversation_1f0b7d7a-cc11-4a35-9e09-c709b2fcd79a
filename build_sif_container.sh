#!/bin/bash

# 简化的SIF容器构建脚本
# 适用于有Linux环境的用户

set -e

echo "=========================================="
echo "sgRNA Analysis SIF Container Builder"
echo "=========================================="

# 检查当前目录
echo "当前工作目录: $(pwd)"
echo "目录内容:"
ls -la

# 检查必需文件
echo ""
echo "检查必需文件..."

required_files=(
    "sgrna_analysis.def"
    "sgrna_analysis_pipeline.py"
    "sgrna_bowtie2_analysis_mis5.py"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file 存在"
    else
        echo "✗ $file 缺失"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo ""
    echo "错误: 缺少以下文件:"
    printf '  %s\n' "${missing_files[@]}"
    echo ""
    echo "请确保以下文件在当前目录中:"
    echo "  - sgrna_analysis.def (容器定义文件)"
    echo "  - sgrna_analysis_pipeline.py (主分析脚本)"
    echo "  - sgrna_bowtie2_analysis_mis5.py (错配分析脚本)"
    exit 1
fi

# 检查Singularity
echo ""
echo "检查Singularity安装..."
if command -v singularity &> /dev/null; then
    echo "✓ Singularity 已安装"
    echo "版本: $(singularity --version)"
else
    echo "✗ Singularity 未安装"
    echo ""
    echo "请先安装Singularity:"
    echo "Ubuntu/Debian:"
    echo "  sudo apt update"
    echo "  sudo apt install singularity-container"
    echo ""
    echo "或从源码编译 (参考官方文档):"
    echo "  https://sylabs.io/guides/3.0/user-guide/installation.html"
    exit 1
fi

# 检查权限
echo ""
echo "检查构建权限..."
if [ "$EUID" -eq 0 ]; then
    echo "✓ 以root权限运行"
    BUILD_CMD="singularity build"
elif singularity build --help 2>&1 | grep -q "fakeroot"; then
    echo "✓ 支持fakeroot构建"
    BUILD_CMD="singularity build --fakeroot"
else
    echo "⚠ 需要sudo权限构建"
    BUILD_CMD="sudo singularity build"
fi

# 检查磁盘空间
echo ""
echo "检查磁盘空间..."
available_space=$(df . | tail -1 | awk '{print $4}')
required_space=5242880  # 5GB in KB

if [ "$available_space" -gt "$required_space" ]; then
    echo "✓ 磁盘空间充足 ($(($available_space/1024/1024))GB 可用)"
else
    echo "⚠ 磁盘空间可能不足 ($(($available_space/1024/1024))GB 可用, 建议5GB+)"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 开始构建
echo ""
echo "=========================================="
echo "开始构建容器..."
echo "=========================================="
echo "这可能需要10-30分钟，取决于网络速度"
echo "构建命令: $BUILD_CMD sgrna_analysis.sif sgrna_analysis.def"
echo ""

# 记录开始时间
start_time=$(date +%s)

# 执行构建
$BUILD_CMD sgrna_analysis.sif sgrna_analysis.def

# 检查构建结果
if [ $? -eq 0 ]; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo "=========================================="
    echo "容器构建成功!"
    echo "=========================================="
    echo "构建时间: $((duration/60))分$((duration%60))秒"
    echo "容器文件: sgrna_analysis.sif"
    echo "文件大小: $(du -h sgrna_analysis.sif | cut -f1)"
    echo ""
    
    # 测试容器
    echo "测试容器..."
    if singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py --help > /dev/null 2>&1; then
        echo "✓ 容器测试通过"
    else
        echo "⚠ 容器测试失败，但文件已生成"
    fi
    
    echo ""
    echo "使用方法:"
    echo "1. 测试容器:"
    echo "   singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py --help"
    echo ""
    echo "2. 运行分析:"
    echo "   singularity exec sgrna_analysis.sif python /opt/sgrna_analysis_pipeline.py \\"
    echo "     library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream' -o results -s sample"
    echo ""
    echo "3. 使用包装脚本 (如果有):"
    echo "   ./run_sgrna_analysis.sh -l library.xlsx -1 R1.fastq.gz -2 R2.fastq.gz -f 'upstream-downstream'"
    echo ""
    
else
    echo ""
    echo "=========================================="
    echo "容器构建失败!"
    echo "=========================================="
    echo "常见问题解决方案:"
    echo "1. 网络问题: 检查网络连接，重试构建"
    echo "2. 权限问题: 尝试使用 sudo 或 --fakeroot"
    echo "3. 空间不足: 清理磁盘空间"
    echo "4. 依赖问题: 检查系统依赖是否完整"
    echo ""
    echo "详细错误信息请查看上面的输出"
    exit 1
fi
