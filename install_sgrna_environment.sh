#!/bin/bash

# sgRNA分析环境完整安装脚本
# 安装所有必需的工具和Python环境

set -e

INSTALL_DIR="$HOME/sgrna_tools"
CONDA_DIR="$INSTALL_DIR/miniconda2"

echo "=========================================="
echo "sgRNA分析环境安装脚本"
echo "=========================================="
echo "安装目录: $INSTALL_DIR"
echo ""

# 创建安装目录
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# 函数：检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 函数：下载文件
download_file() {
    local url="$1"
    local output="$2"
    
    if command_exists wget; then
        wget -q --show-progress "$url" -O "$output"
    elif command_exists curl; then
        curl -L "$url" -o "$output"
    else
        echo "错误: 需要wget或curl来下载文件"
        exit 1
    fi
}

echo "1. 安装Miniconda2 (Python 2.7环境)..."
if [ ! -d "$CONDA_DIR" ]; then
    echo "  下载Miniconda2..."
    download_file "https://repo.anaconda.com/miniconda/Miniconda2-latest-Linux-x86_64.sh" "miniconda2.sh"
    
    echo "  安装Miniconda2..."
    bash miniconda2.sh -b -p "$CONDA_DIR"
    rm miniconda2.sh
    
    echo "  ✓ Miniconda2安装完成"
else
    echo "  ✓ Miniconda2已存在"
fi

# 设置conda环境
export PATH="$CONDA_DIR/bin:$PATH"

echo ""
echo "2. 安装Python包..."
echo "  更新conda..."
conda update -n base -c defaults conda -y

echo "  安装基础包..."
conda install -y \
    pandas=0.24.2 \
    numpy=1.16.6 \
    matplotlib=2.2.5 \
    openpyxl=2.6.4

echo "  安装pip包..."
pip install \
    pysam==0.15.4 \
    seaborn==0.9.1 \
    mageck==*******

echo "  ✓ Python包安装完成"

echo ""
echo "3. 安装FLASH..."
if [ ! -f "$INSTALL_DIR/bin/flash" ]; then
    mkdir -p "$INSTALL_DIR/bin"
    
    echo "  下载FLASH..."
    download_file "https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz" "flash.tar.gz"
    
    echo "  解压安装..."
    tar -xzf flash.tar.gz
    cp FLASH-1.2.11-Linux-x86_64/flash "$INSTALL_DIR/bin/"
    chmod +x "$INSTALL_DIR/bin/flash"
    rm -rf flash.tar.gz FLASH-1.2.11-Linux-x86_64
    
    echo "  ✓ FLASH安装完成"
else
    echo "  ✓ FLASH已存在"
fi

echo ""
echo "4. 安装bowtie2..."
if [ ! -f "$INSTALL_DIR/bin/bowtie2" ]; then
    echo "  下载bowtie2..."
    download_file "https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip" "bowtie2.zip"
    
    echo "  解压安装..."
    unzip -q bowtie2.zip
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* "$INSTALL_DIR/bin/"
    chmod +x "$INSTALL_DIR/bin/bowtie2"*
    rm -rf bowtie2.zip bowtie2-2.4.5-linux-x86_64
    
    echo "  ✓ bowtie2安装完成"
else
    echo "  ✓ bowtie2已存在"
fi

echo ""
echo "5. 安装samtools..."
if [ ! -f "$INSTALL_DIR/bin/samtools" ]; then
    echo "  下载samtools..."
    download_file "https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2" "samtools.tar.bz2"
    
    echo "  编译安装..."
    tar -xjf samtools.tar.bz2
    cd samtools-1.15.1
    
    # 简化配置，避免依赖问题
    ./configure --prefix="$INSTALL_DIR" --disable-bz2 --disable-lzma --disable-plugins
    make -j$(nproc) && make install
    
    cd "$INSTALL_DIR"
    rm -rf samtools.tar.bz2 samtools-1.15.1
    
    echo "  ✓ samtools安装完成"
else
    echo "  ✓ samtools已存在"
fi

echo ""
echo "6. 创建环境激活脚本..."
cat > "$INSTALL_DIR/activate_sgrna_env.sh" << EOF
#!/bin/bash
# sgRNA分析环境激活脚本

export SGRNA_TOOLS_DIR="$INSTALL_DIR"
export PATH="$INSTALL_DIR/bin:$CONDA_DIR/bin:\$PATH"
export PYTHONPATH="$INSTALL_DIR:\$PYTHONPATH"

echo "sgRNA分析环境已激活"
echo "工具目录: $INSTALL_DIR"
echo ""
echo "可用工具:"
echo "  Python: \$(which python)"
echo "  FLASH: \$(which flash)"
echo "  bowtie2: \$(which bowtie2)"
echo "  samtools: \$(which samtools)"
echo "  MAGeCK: \$(which mageck)"
echo ""
EOF

chmod +x "$INSTALL_DIR/activate_sgrna_env.sh"

echo ""
echo "7. 创建运行脚本..."
cat > "$INSTALL_DIR/run_sgrna_analysis.sh" << 'EOF'
#!/bin/bash

# sgRNA分析运行脚本

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 激活环境
source "$SCRIPT_DIR/activate_sgrna_env.sh"

# 检查参数
if [ $# -lt 4 ]; then
    echo "用法: $0 <library.xlsx> <R1.fastq.gz> <R2.fastq.gz> <flanking_sequences> [options]"
    echo ""
    echo "参数:"
    echo "  library.xlsx        sgRNA库文件"
    echo "  R1.fastq.gz         R1测序文件"
    echo "  R2.fastq.gz         R2测序文件"
    echo "  flanking_sequences  引物序列 (格式: 'upstream-downstream')"
    echo ""
    echo "选项:"
    echo "  -o OUTPUT_DIR       输出目录 (默认: results)"
    echo "  -s SAMPLE_NAME      样本名称 (默认: 从R1文件名提取)"
    echo ""
    echo "示例:"
    echo "  $0 library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample"
    exit 1
fi

# 检查主脚本是否存在
MAIN_SCRIPT="$SCRIPT_DIR/sgrna_analysis_pipeline.py"
if [ ! -f "$MAIN_SCRIPT" ]; then
    echo "错误: 主脚本不存在: $MAIN_SCRIPT"
    echo "请将sgrna_analysis_pipeline.py复制到: $SCRIPT_DIR"
    exit 1
fi

# 运行分析
echo "=========================================="
echo "运行sgRNA分析流程"
echo "=========================================="
echo "使用环境: $SCRIPT_DIR"
echo "主脚本: $MAIN_SCRIPT"
echo ""

python "$MAIN_SCRIPT" "$@"
EOF

chmod +x "$INSTALL_DIR/run_sgrna_analysis.sh"

echo ""
echo "8. 测试安装..."
source "$INSTALL_DIR/activate_sgrna_env.sh"

echo "  测试Python包..."
python -c "import pandas, numpy, matplotlib, pysam, openpyxl, seaborn; print('✓ Python包正常')"

echo "  测试工具..."
flash --help > /dev/null 2>&1 && echo "✓ FLASH正常" || echo "✗ FLASH有问题"
bowtie2 --help > /dev/null 2>&1 && echo "✓ bowtie2正常" || echo "✗ bowtie2有问题"
samtools --help > /dev/null 2>&1 && echo "✓ samtools正常" || echo "✗ samtools有问题"
mageck --help > /dev/null 2>&1 && echo "✓ MAGeCK正常" || echo "✗ MAGeCK有问题"

echo ""
echo "=========================================="
echo "安装完成!"
echo "=========================================="
echo ""
echo "安装目录: $INSTALL_DIR"
echo "总大小: $(du -sh $INSTALL_DIR | cut -f1)"
echo ""
echo "使用方法:"
echo ""
echo "1. 复制主脚本到安装目录:"
echo "   cp sgrna_analysis_pipeline.py $INSTALL_DIR/"
echo "   cp sgrna_bowtie2_analysis_mis5.py $INSTALL_DIR/"
echo ""
echo "2. 激活环境:"
echo "   source $INSTALL_DIR/activate_sgrna_env.sh"
echo ""
echo "3. 运行分析:"
echo "   $INSTALL_DIR/run_sgrna_analysis.sh library.xlsx R1.fastq.gz R2.fastq.gz 'upstream-downstream'"
echo ""
echo "或者直接使用:"
echo "   $INSTALL_DIR/run_sgrna_analysis.sh library.xlsx R1.fastq.gz R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC' -o results -s sample"
echo ""
