#!/bin/bash

# 使用SIF容器运行sgRNA分析的简化脚本

set -e

# 默认值
CONTAINER="sgrna_analysis.sif"
OUTPUT_DIR="results"

# 显示使用方法
show_usage() {
    echo "使用SIF容器运行sgRNA分析"
    echo ""
    echo "用法:"
    echo "  $0 <library.xlsx> <R1.fastq.gz> <R2.fastq.gz> <flanking_sequences> [options]"
    echo ""
    echo "参数:"
    echo "  library.xlsx        sgRNA库文件"
    echo "  R1.fastq.gz         R1测序文件"
    echo "  R2.fastq.gz         R2测序文件"
    echo "  flanking_sequences  引物序列 (格式: 'upstream-downstream')"
    echo ""
    echo "选项:"
    echo "  -o OUTPUT_DIR       输出目录 (默认: results)"
    echo "  -s SAMPLE_NAME      样本名称 (默认: 从R1文件名提取)"
    echo "  -c CONTAINER        容器文件 (默认: sgrna_analysis.sif)"
    echo "  -h                  显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 library.xlsx sample_R1.fastq.gz sample_R2.fastq.gz 'CACCG-GTTTTAGAGCTAGAAATAGC'"
    echo "  $0 lib.xlsx R1.fq.gz R2.fq.gz 'UP-DOWN' -o my_results -s my_sample"
}

# 解析命令行参数
if [ $# -lt 4 ]; then
    echo "错误: 参数不足"
    echo ""
    show_usage
    exit 1
fi

LIBRARY="$1"
R1_FASTQ="$2"
R2_FASTQ="$3"
FLANKING="$4"
shift 4

# 解析选项
while [[ $# -gt 0 ]]; do
    case $1 in
        -o)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -s)
            SAMPLE_NAME="$2"
            shift 2
            ;;
        -c)
            CONTAINER="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 检查文件是否存在
echo "检查输入文件..."
for file in "$LIBRARY" "$R1_FASTQ" "$R2_FASTQ" "$CONTAINER"; do
    if [ ! -f "$file" ]; then
        echo "错误: 文件不存在: $file"
        exit 1
    fi
    echo "✓ $file"
done

# 提取样本名称
if [ -z "$SAMPLE_NAME" ]; then
    SAMPLE_NAME=$(basename "$R1_FASTQ" | sed 's/_R1.*$//' | sed 's/\.fastq.*$//' | sed 's/\.fq.*$//')
    echo "自动检测样本名称: $SAMPLE_NAME"
fi

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 获取绝对路径
LIBRARY_ABS=$(realpath "$LIBRARY")
R1_FASTQ_ABS=$(realpath "$R1_FASTQ")
R2_FASTQ_ABS=$(realpath "$R2_FASTQ")
OUTPUT_DIR_ABS=$(realpath "$OUTPUT_DIR")
CONTAINER_ABS=$(realpath "$CONTAINER")

echo ""
echo "=========================================="
echo "sgRNA分析参数"
echo "=========================================="
echo "库文件:     $LIBRARY_ABS"
echo "R1文件:     $R1_FASTQ_ABS"
echo "R2文件:     $R2_FASTQ_ABS"
echo "引物序列:   $FLANKING"
echo "输出目录:   $OUTPUT_DIR_ABS"
echo "样本名称:   $SAMPLE_NAME"
echo "容器文件:   $CONTAINER_ABS"
echo "=========================================="

# 检查容器
echo ""
echo "测试容器..."
if singularity exec "$CONTAINER_ABS" python /opt/sgrna_analysis_pipeline.py --help > /dev/null 2>&1; then
    echo "✓ 容器测试通过"
else
    echo "✗ 容器测试失败"
    echo "请检查容器文件是否正确构建"
    exit 1
fi

# 运行分析
echo ""
echo "开始分析..."
echo "这可能需要几分钟到几小时，取决于数据大小"
echo ""

# 记录开始时间
start_time=$(date +%s)

# 执行分析
singularity exec \
    --bind "$OUTPUT_DIR_ABS:/output" \
    --bind "$(dirname "$LIBRARY_ABS"):/library_dir" \
    --bind "$(dirname "$R1_FASTQ_ABS"):/fastq_dir" \
    --pwd /output \
    "$CONTAINER_ABS" \
    python /opt/sgrna_analysis_pipeline.py \
    "/library_dir/$(basename "$LIBRARY_ABS")" \
    "/fastq_dir/$(basename "$R1_FASTQ_ABS")" \
    "/fastq_dir/$(basename "$R2_FASTQ_ABS")" \
    "$FLANKING" \
    -o /output \
    -s "$SAMPLE_NAME"

# 检查结果
if [ $? -eq 0 ]; then
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo ""
    echo "=========================================="
    echo "分析完成!"
    echo "=========================================="
    echo "运行时间: $((duration/60))分$((duration%60))秒"
    echo "输出目录: $OUTPUT_DIR_ABS"
    echo ""
    
    # 显示输出文件
    echo "生成的文件:"
    if [ -d "$OUTPUT_DIR_ABS" ]; then
        for file in "$OUTPUT_DIR_ABS"/*; do
            if [ -f "$file" ]; then
                size=$(du -h "$file" | cut -f1)
                echo "  $(basename "$file") ($size)"
            fi
        done
    fi
    
    # 检查关键输出文件
    echo ""
    key_files=("Sample.stat.xlsx" "${SAMPLE_NAME}.png" "${SAMPLE_NAME}.count.txt")
    for file in "${key_files[@]}"; do
        if [ -f "$OUTPUT_DIR_ABS/$file" ]; then
            echo "✓ $file 已生成"
        else
            echo "⚠ $file 未找到"
        fi
    done
    
    echo ""
    echo "分析成功完成!"
    
else
    echo ""
    echo "=========================================="
    echo "分析失败!"
    echo "=========================================="
    echo "请检查上面的错误信息"
    echo ""
    echo "常见问题:"
    echo "1. 输入文件格式不正确"
    echo "2. 引物序列格式错误"
    echo "3. 磁盘空间不足"
    echo "4. 内存不足"
    exit 1
fi
