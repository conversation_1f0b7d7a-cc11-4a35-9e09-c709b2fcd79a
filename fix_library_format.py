#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
修复库文件格式，确保有3列供MAGeCK使用
"""

import pandas as pd
import sys

def fix_library_csv(input_file, output_file=None):
    """修复库文件格式"""
    if output_file is None:
        output_file = input_file
    
    print("修复库文件: %s" % input_file)
    
    # 读取现有文件
    df = pd.read_csv(input_file)
    
    print("原始格式:")
    print("  列数: %d" % len(df.columns))
    print("  列名: %s" % list(df.columns))
    print("  行数: %d" % len(df))
    
    # 确保有3列
    if len(df.columns) == 2:
        # 只有2列，添加Gene列
        df['Gene'] = df.iloc[:, 0]  # 使用sgRNA_ID作为Gene
        print("添加Gene列 (使用sgRNA_ID)")
    elif len(df.columns) >= 3:
        # 已经有3列或更多，保留前3列
        df = df.iloc[:, :3]
        print("保留前3列")
    
    # 重命名列
    df.columns = ['sgRNA_ID', 'sgRNA_sequence', 'Gene']
    
    # 保存为无header的CSV (MAGeCK格式)
    df.to_csv(output_file, index=False, header=False)
    
    print("修复后格式:")
    print("  列数: 3")
    print("  格式: sgRNA_ID, sgRNA_sequence, Gene")
    print("  保存到: %s" % output_file)
    
    # 显示前几行
    print("前3行:")
    for i, row in df.head(3).iterrows():
        print("  %s,%s,%s" % (row['sgRNA_ID'], row['sgRNA_sequence'], row['Gene']))

def main():
    if len(sys.argv) < 2:
        print("用法: python fix_library_format.py <input_csv> [output_csv]")
        print("示例: python fix_library_format.py PL808-NGS/lib.csv")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else input_file
    
    fix_library_csv(input_file, output_file)
    print("修复完成!")

if __name__ == '__main__':
    main()
