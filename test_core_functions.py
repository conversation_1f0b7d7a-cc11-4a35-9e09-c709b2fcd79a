#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
Test core functions of sgRNA Analysis Pipeline
This script tests the core functionality without requiring external tools
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# Import functions from the main pipeline
from sgrna_analysis_pipeline import (
    process_excel_to_csv,
    extract_sgrna_from_read,
    process_fastq_file,
    write_fastq_records,
    create_reference_fasta,
    calculate_skew_ratio,
    plot_cumulative_distribution,
    calculate_base_error_rate,
    generate_summary_report,
    parse_mageck_log
)

def create_test_data():
    """Create comprehensive test data"""
    print("Creating test data...")
    
    # Create Excel library
    library_data = {
        'sgRNA_ID': ['gRNA_%d' % i for i in range(1, 21)],
        'sgRNA_sequence': [
            'ATCGATCGATCGATCGATCG',  # 20 bp each
            'GCGCGCGCGCGCGCGCGCGC',
            'ATATATATATATATATATAT',
            'CGCGCGCGCGCGCGCGCGCG',
            'TATATATATATATATATATA',
            'AAAAAAAAAAAAAAAAAAAT',
            'TTTTTTTTTTTTTTTTTTTT',
            'GGGGGGGGGGGGGGGGGGGG',
            'CCCCCCCCCCCCCCCCCCCC',
            'ACGTACGTACGTACGTACGT',
            'TGCATGCATGCATGCATGCA',
            'AGTCAGTCAGTCAGTCAGTC',
            'CTGACTGACTGACTGACTGA',
            'GATCGATCGATCGATCGATC',
            'CTAGCTAGCTAGCTAGCTAG',
            'AGCTAGCTAGCTAGCTAGCT',
            'TCGATCGATCGATCGATCGA',
            'GCATGCATGCATGCATGCAT',
            'CATGCATGCATGCATGCATG',
            'TACGTACGTACGTACGTACG'
        ],
        'Gene': ['Gene%d' % i for i in range(1, 21)]
    }
    
    df = pd.DataFrame(library_data)
    excel_file = 'test_comprehensive_library.xlsx'
    df.to_excel(excel_file, index=False)
    
    # Create test FASTQ with various scenarios
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    
    fastq_content = []
    read_id = 1
    
    # Add reads for each sgRNA with different abundances
    for i, sgrna in enumerate(library_data['sgRNA_sequence']):
        # Simulate different abundance levels
        abundance = max(1, int(1000 * np.exp(-i * 0.3)))  # Exponential decay
        
        for copy in range(abundance):
            # Forward orientation
            full_seq = upstream + sgrna + downstream + "EXTRA"
            qual = "I" * len(full_seq)
            
            fastq_content.append("@read_%d\n%s\n+\n%s\n" % (read_id, full_seq, qual))
            read_id += 1
    
    # Add some noise reads (no sgRNA match)
    for i in range(100):
        noise_seq = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA"
        qual = "I" * len(noise_seq)
        fastq_content.append("@noise_%d\n%s\n+\n%s\n" % (i, noise_seq, qual))
        read_id += 1
    
    fastq_file = 'test_comprehensive.fastq'
    with open(fastq_file, 'w') as f:
        f.writelines(fastq_content)
    
    print("  Created library: %s (%d sgRNAs)" % (excel_file, len(library_data['sgRNA_ID'])))
    print("  Created FASTQ: %s (%d reads)" % (fastq_file, len(fastq_content)))
    
    return excel_file, fastq_file

def create_mock_count_file(extracted_reads, library_csv):
    """Create a mock count file based on extracted reads"""
    print("Creating mock count file...")
    
    # Load library
    lib_df = pd.read_csv(library_csv)
    sgrna_to_gene = dict(zip(lib_df['sgRNA_ID'], lib_df.get('Gene', lib_df['sgRNA_ID'])))
    
    # Count extracted sequences
    sequence_counts = {}
    for _, seq, _ in extracted_reads:
        sequence_counts[seq] = sequence_counts.get(seq, 0) + 1
    
    # Map sequences back to sgRNA IDs
    seq_to_id = dict(zip(lib_df['sgRNA_sequence'], lib_df['sgRNA_ID']))
    
    count_data = []
    for seq, count in sequence_counts.items():
        if seq in seq_to_id:
            sgrna_id = seq_to_id[seq]
            gene = sgrna_to_gene.get(sgrna_id, sgrna_id)
            count_data.append([sgrna_id, gene, count])
    
    # Add zero counts for missing sgRNAs
    for _, row in lib_df.iterrows():
        sgrna_id = row['sgRNA_ID']
        if row['sgRNA_sequence'] not in sequence_counts:
            gene = sgrna_to_gene.get(sgrna_id, sgrna_id)
            count_data.append([sgrna_id, gene, 0])
    
    # Create count file
    count_file = 'test_sample.count.txt'
    with open(count_file, 'w') as f:
        f.write("sgRNA\tGene\ttest_sample\n")
        for sgrna_id, gene, count in count_data:
            f.write("%s\t%s\t%d\n" % (sgrna_id, gene, count))
    
    print("  Created count file: %s" % count_file)
    return count_file

def create_mock_log_file():
    """Create a mock MAGeCK log file"""
    print("Creating mock log file...")
    
    log_content = """INFO  @ Wed, 23 Jul 2025 12:00:00: Parameters: mageck count -l lib.csv -n test_sample --sample-label test_sample --fastq test_sample.bam
INFO  @ Wed, 23 Jul 2025 12:00:00: Welcome to MAGeCK v0.5.7. Command: count
INFO  @ Wed, 23 Jul 2025 12:00:00: Loading 20 predefined sgRNAs.
WARNING @ Wed, 23 Jul 2025 12:00:00: There are 0 sgRNAs with duplicated sequences.
INFO  @ Wed, 23 Jul 2025 12:00:00: Parsing BAM file test_sample.bam...
INFO  @ Wed, 23 Jul 2025 12:00:00: 20 references detected in the BAM file.
INFO  @ Wed, 23 Jul 2025 12:00:00: Processing 0M records ..
INFO  @ Wed, 23 Jul 2025 12:00:05: Processing 1M records ..
INFO  @ Wed, 23 Jul 2025 12:00:10: Processing 2M records ..
INFO  @ Wed, 23 Jul 2025 12:00:15: mapped:8500
DEBUG @ Wed, 23 Jul 2025 12:00:16: Initial (total) size factor: 1.0
DEBUG @ Wed, 23 Jul 2025 12:00:16: Median factor: 1.0000962093515486
INFO  @ Wed, 23 Jul 2025 12:00:16: Final size factor: 1.0000962093515486
INFO  @ Wed, 23 Jul 2025 12:00:16: Summary of file test_sample.bam:
INFO  @ Wed, 23 Jul 2025 12:00:16: label        test_sample
INFO  @ Wed, 23 Jul 2025 12:00:16: reads        9000
INFO  @ Wed, 23 Jul 2025 12:00:16: mappedreads  8500
INFO  @ Wed, 23 Jul 2025 12:00:16: totalsgrnas  20
INFO  @ Wed, 23 Jul 2025 12:00:16: zerosgrnas   2
INFO  @ Wed, 23 Jul 2025 12:00:16: giniindex    0.45
"""
    
    log_file = 'test_sample.log'
    with open(log_file, 'w') as f:
        f.write(log_content)
    
    print("  Created log file: %s" % log_file)
    return log_file

def create_mock_mismatch_stats():
    """Create mock mismatch statistics"""
    return {
        '0_mismatch': 8000,
        '1_mismatch': 400,
        '2_mismatch': 80,
        '3_mismatch': 15,
        '4_mismatch': 4,
        '5_mismatch': 1,
        'indel': 10,
        'more_than_5_mismatch': 0,
        'unmapped': 500
    }

def test_comprehensive_pipeline():
    """Test the comprehensive pipeline with mock data"""
    print("=" * 60)
    print("Testing Comprehensive sgRNA Analysis Pipeline")
    print("=" * 60)
    
    # Create test data
    excel_file, fastq_file = create_test_data()
    
    # Step 1: Excel to CSV conversion
    print("\n1. Testing Excel to CSV conversion...")
    library_csv = 'test_lib.csv'
    min_len, max_len = process_excel_to_csv(excel_file, library_csv)
    print("   sgRNA length range: %d-%d" % (min_len, max_len))
    
    # Step 2: sgRNA extraction
    print("\n2. Testing sgRNA extraction...")
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    
    extracted_reads = process_fastq_file(fastq_file, upstream, downstream, min_len, max_len)
    print("   Extracted %d reads from %d total reads" % (len(extracted_reads), 
          sum(1 for line in open(fastq_file) if line.startswith('@'))))
    
    # Step 3: Write extracted reads
    extracted_file = 'test_extracted.fq'
    write_fastq_records(extracted_reads, extracted_file)
    print("   Wrote extracted reads to: %s" % extracted_file)
    
    # Step 4: Create reference FASTA
    print("\n3. Testing reference FASTA creation...")
    reference_fasta = 'test_reference.fasta'
    create_reference_fasta(library_csv, reference_fasta)
    
    # Step 5: Create mock count file and calculate skew ratio
    print("\n4. Testing skew ratio calculation...")
    count_file = create_mock_count_file(extracted_reads, library_csv)
    skew_ratio, idx_10, idx_90, counts = calculate_skew_ratio(count_file)
    print("   Skew ratio: %.2f" % skew_ratio)
    print("   10%% position: %d, 90%% position: %d" % (idx_10, idx_90))
    
    # Step 6: Create cumulative distribution plot
    print("\n5. Testing plot generation...")
    plot_file = 'test_distribution.png'
    plot_cumulative_distribution(counts, idx_10, idx_90, skew_ratio, plot_file)
    
    # Step 7: Test log parsing
    print("\n6. Testing log file parsing...")
    log_file = create_mock_log_file()
    stats = parse_mageck_log(log_file)
    print("   Parsed stats: %s" % stats)
    
    # Step 8: Calculate base error rate
    print("\n7. Testing base error rate calculation...")
    mismatch_stats = create_mock_mismatch_stats()
    error_rate = calculate_base_error_rate(mismatch_stats, count_file, library_csv, upstream, downstream)
    print("   Base error rate: %.4f" % error_rate)
    
    # Step 9: Generate final report
    print("\n8. Testing report generation...")
    report_file = 'Test_Sample.stat.xls'
    generate_summary_report(stats, skew_ratio, error_rate, report_file)
    
    # Cleanup
    print("\n9. Cleaning up test files...")
    test_files = [
        excel_file, fastq_file, library_csv, extracted_file, 
        reference_fasta, count_file, log_file, plot_file, report_file
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print("   Removed: %s" % file)
    
    print("\n" + "=" * 60)
    print("Comprehensive pipeline test completed successfully!")
    print("All core functions are working properly.")
    print("=" * 60)

if __name__ == '__main__':
    test_comprehensive_pipeline()
