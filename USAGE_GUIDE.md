# sgRNA Analysis Pipeline - 使用指南

## 概述

这是一个完整的sgRNA测序数据分析流程，专为Python2环境设计，能够处理PE150测序数据并生成全面的质量控制报告。

## 主要功能

1. **Excel库文件处理** - 将sgRNA库Excel文件转换为CSV格式
2. **序列合并** - 使用FLASH合并双端测序reads
3. **sgRNA提取** - 基于上下游引物序列提取目标sgRNA
4. **序列比对** - 使用bowtie2进行参考序列比对
5. **计数分析** - 使用MAGeCK进行reads计数
6. **质量分析** - 分析错配、插入缺失等错误
7. **统计计算** - 计算覆盖率、偏斜比等指标
8. **可视化** - 生成累积分布图
9. **报告生成** - 输出Excel格式的综合报告

## 文件结构

```
CRISPRQC/
├── sgrna_analysis_pipeline.py    # 主分析流程
├── sgrna_bowtie2_analysis_mis5.py # 错配分析模块
├── test_pipeline.py              # 基础功能测试
├── test_core_functions.py        # 综合功能测试
├── example_usage.py              # 使用示例
├── README.md                     # 详细说明文档
└── USAGE_GUIDE.md                # 本使用指南
```

## 快速开始

### 1. 环境准备

**必需软件：**
- Python 2.7
- FLASH (序列合并)
- bowtie2 (序列比对)
- samtools (BAM文件处理)
- MAGeCK (计数分析)

**Python依赖：**
```bash
pip install pandas numpy matplotlib pysam openpyxl
```

### 2. 输入文件准备

**必需输入：**
1. **sgRNA库文件** (Excel格式)
   - 第一列：sgRNA编号
   - 第二列：sgRNA序列
   - 其他列：可选信息

2. **测序文件** (FASTQ格式，可压缩)
   - R1文件：正向reads
   - R2文件：反向reads

3. **引物序列** (文本格式)
   - 格式：`上游序列-下游序列`
   - 例如：`CACCG-GTTTTAGAGCTAGAAATAGC`

### 3. 运行流程

**基本命令：**
```bash
python sgrna_analysis_pipeline.py \
    library.xlsx \
    sample_R1.fastq.gz \
    sample_R2.fastq.gz \
    "CACCG-GTTTTAGAGCTAGAAATAGC" \
    -o results/ \
    -s sample_name
```

**参数说明：**
- `library.xlsx`: sgRNA库文件
- `sample_R1.fastq.gz`: R1测序文件
- `sample_R2.fastq.gz`: R2测序文件
- `"上游-下游"`: 引物序列
- `-o results/`: 输出目录
- `-s sample_name`: 样本名称

## 输出文件说明

### 主要输出

1. **Sample.stat.xlsx** - 最终统计报告
   - 测序reads数量
   - 测序深度
   - 正确序列比率
   - 覆盖率统计
   - 偏斜比
   - 碱基错误率

2. **sample.png** - 累积分布图
   - 显示sgRNA丰度分布
   - 标记10%和90%位置
   - 显示偏斜比数值

### 中间文件

- `lib.csv`: 转换后的库文件
- `sample.extendedFrags.fastq`: 合并的reads
- `sample.combined.fq`: 提取的sgRNA序列
- `sample.bam`: 比对结果
- `sample.count.txt`: 计数结果
- `sample.log`: MAGeCK日志

## 关键算法

### sgRNA提取策略

1. **正向搜索**: `上游[sgRNA]下游`
2. **反向搜索**: `下游反向互补[sgRNA反向互补]上游反向互补`
3. **未合并reads处理**: 
   - 先搜索R1
   - 如失败，搜索对应的R2

### 统计指标计算

**偏斜比 (Skew Ratio):**
```
偏斜比 = 90%累积位置的gRNA数量 / 10%累积位置的gRNA数量
```

**碱基错误率:**
```
错误率 = 总错配数 / 总碱基数
总错配数 = 1×(1错配) + 2×(2错配) + ... + 5×(5错配) + 1×(插入缺失)
总碱基数 = Σ(sgRNA长度 + 引物长度) × reads数
```

**覆盖率:**
```
覆盖率 = (总gRNA数 - 零reads的gRNA数) / 总gRNA数
```

## 测试验证

### 运行基础测试
```bash
python test_pipeline.py
```

### 运行综合测试
```bash
python test_core_functions.py
```

### 生成示例数据
```bash
python example_usage.py
```

## 故障排除

### 常见问题

1. **FLASH未找到**
   - 确保FLASH已安装并在PATH中
   - 下载地址：https://ccb.jhu.edu/software/FLASH/

2. **bowtie2未找到**
   - 确保bowtie2已安装并在PATH中
   - 下载地址：http://bowtie-bio.sourceforge.net/bowtie2/

3. **内存不足**
   - 大文件可能需要更多内存
   - 考虑使用高内存服务器

4. **Excel读取错误**
   - 确保Excel文件格式正确
   - 第一列必须是sgRNA ID，第二列必须是序列

### 性能优化

1. **使用压缩文件** - FASTQ.gz文件节省磁盘空间
2. **充足磁盘空间** - 确保有足够空间存储中间文件
3. **并行处理** - 可以同时处理多个样本

## 输出示例

**Sample.stat.xlsx内容示例：**
```
Sample                    Results
Sequencing Reads Amount   9,000
Sequencing Depth          450X
Correct Sequence rate     94.44%
Total gRNAs               20
Zero gRNAs                2
Coverage_rate             90.00%
Skew Ration               8.00
Base Error rate           0.47%
```

## 技术支持

如遇到问题，请检查：
1. 所有依赖软件是否正确安装
2. 输入文件格式是否符合要求
3. 引物序列是否正确
4. 系统资源是否充足

## 版本信息

- 版本：1.0
- 兼容性：Python 2.7
- 最后更新：2025-07-23
