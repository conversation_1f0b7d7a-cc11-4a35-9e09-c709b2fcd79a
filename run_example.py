#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
Quick example runner for sgRNA Analysis Pipeline
This script creates sample data and shows how to run the pipeline
"""

import os
import sys
import subprocess
import pandas as pd
import gzip

def create_minimal_test_data():
    """Create minimal test data for demonstration"""
    print("Creating minimal test data...")
    
    # Create a small sgRNA library
    library_data = {
        'sgRNA_ID': ['gRNA_1', 'gRNA_2', 'gRNA_3', 'gRNA_4', 'gRNA_5'],
        'sgRNA_sequence': [
            'ATCGATCGATCGATCGATCG',  # 20 bp
            'GCGCGCGCGCGCGCGCGCGC',  # 20 bp
            'ATATATATATATATATATAT',  # 20 bp
            'CGCGCGCGCGCGCGCGCGCG',  # 20 bp
            'TATATATATATATATATATA'   # 20 bp
        ],
        'Gene': ['Gene1', 'Gene2', 'Gene3', 'Gene4', 'Gene5']
    }
    
    df = pd.DataFrame(library_data)
    excel_file = 'demo_library.xlsx'
    df.to_excel(excel_file, index=False)
    
    # Create small FASTQ files
    upstream = "CACCG"
    downstream = "GTTTTAGAGC"
    
    def reverse_complement(seq):
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(seq.upper()))
    
    # Generate reads
    r1_reads = []
    r2_reads = []
    
    read_id = 1
    for i, sgrna in enumerate(library_data['sgRNA_sequence']):
        # Create 20 copies of each sgRNA
        for copy in range(20):
            full_construct = upstream + sgrna + downstream
            
            # Create overlapping PE reads
            r1_seq = full_construct[:35]  # First 35 bp
            r2_seq = full_construct[15:]  # Last part with 20 bp overlap
            r2_seq = reverse_complement(r2_seq)
            
            r1_qual = "I" * len(r1_seq)
            r2_qual = "I" * len(r2_seq)
            
            r1_reads.append("@read_%d/1\n%s\n+\n%s\n" % (read_id, r1_seq, r1_qual))
            r2_reads.append("@read_%d/2\n%s\n+\n%s\n" % (read_id, r2_seq, r2_qual))
            
            read_id += 1
    
    # Write FASTQ files
    r1_file = 'demo_R1.fastq'
    r2_file = 'demo_R2.fastq'
    
    with open(r1_file, 'w') as f:
        f.writelines(r1_reads)
    
    with open(r2_file, 'w') as f:
        f.writelines(r2_reads)
    
    print("  Created library: %s (%d sgRNAs)" % (excel_file, len(library_data['sgRNA_ID'])))
    print("  Created R1: %s (%d reads)" % (r1_file, len(r1_reads)))
    print("  Created R2: %s (%d reads)" % (r2_file, len(r2_reads)))
    
    return excel_file, r1_file, r2_file

def check_dependencies():
    """Check if required software is available"""
    print("Checking dependencies...")
    
    required_tools = ['flash', 'bowtie2', 'samtools', 'mageck']
    missing_tools = []
    
    for tool in required_tools:
        try:
            subprocess.check_output([tool, '--help'], stderr=subprocess.STDOUT)
            print("  %s: ✓" % tool)
        except (subprocess.CalledProcessError, OSError):
            print("  %s: ✗ (not found)" % tool)
            missing_tools.append(tool)
    
    if missing_tools:
        print("\nMissing tools: %s" % ', '.join(missing_tools))
        print("Please install these tools before running the pipeline.")
        return False
    
    print("All dependencies found!")
    return True

def run_pipeline_demo():
    """Run the pipeline demonstration"""
    print("=" * 60)
    print("sgRNA Analysis Pipeline - Demo Run")
    print("=" * 60)
    
    # Check dependencies
    if not check_dependencies():
        print("\nDemo cannot run without required dependencies.")
        print("You can still test core functions with: python test_core_functions.py")
        return
    
    # Create test data
    excel_file, r1_file, r2_file = create_minimal_test_data()
    
    # Set parameters
    flanking_sequences = "CACCG-GTTTTAGAGC"
    output_dir = "demo_output"
    sample_name = "demo_sample"
    
    print("\nRunning pipeline with parameters:")
    print("  Library: %s" % excel_file)
    print("  R1 FASTQ: %s" % r1_file)
    print("  R2 FASTQ: %s" % r2_file)
    print("  Flanking: %s" % flanking_sequences)
    print("  Output: %s" % output_dir)
    print("  Sample: %s" % sample_name)
    
    # Run the pipeline
    cmd = [
        'python', 'sgrna_analysis_pipeline.py',
        excel_file,
        r1_file,
        r2_file,
        flanking_sequences,
        '-o', output_dir,
        '-s', sample_name
    ]
    
    print("\nExecuting command:")
    print("  %s" % ' '.join(cmd))
    print("\nPipeline output:")
    print("-" * 40)
    
    try:
        subprocess.check_call(cmd)
        print("-" * 40)
        print("Pipeline completed successfully!")
        
        # Show output files
        if os.path.exists(output_dir):
            print("\nGenerated files in '%s':" % output_dir)
            for file in sorted(os.listdir(output_dir)):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print("  %s (%d bytes)" % (file, size))
            
            # Show final report if exists
            report_file = os.path.join(output_dir, 'Sample.stat.xlsx')
            if os.path.exists(report_file):
                print("\nFinal report content:")
                try:
                    df = pd.read_excel(report_file)
                    for _, row in df.iterrows():
                        print("  %-25s: %s" % (row['Sample'], row['Results']))
                except Exception as e:
                    print("  Could not read report: %s" % str(e))
        
    except subprocess.CalledProcessError as e:
        print("-" * 40)
        print("Pipeline failed with error code: %d" % e.returncode)
        print("Please check the error messages above.")
    
    except Exception as e:
        print("-" * 40)
        print("Error running pipeline: %s" % str(e))
    
    # Cleanup option
    print("\nCleanup:")
    cleanup = raw_input("Remove demo files? (y/n): ").lower().strip()
    if cleanup == 'y':
        demo_files = [excel_file, r1_file, r2_file]
        for file in demo_files:
            if os.path.exists(file):
                os.remove(file)
                print("  Removed: %s" % file)
        
        if os.path.exists(output_dir):
            import shutil
            shutil.rmtree(output_dir)
            print("  Removed directory: %s" % output_dir)
        
        print("Demo files cleaned up.")
    else:
        print("Demo files preserved for inspection.")
    
    print("\n" + "=" * 60)
    print("Demo completed!")
    print("=" * 60)

def show_help():
    """Show help information"""
    print("sgRNA Analysis Pipeline - Demo Runner")
    print("")
    print("Usage:")
    print("  python run_example.py          # Run full demo (requires external tools)")
    print("  python test_core_functions.py  # Test core functions only")
    print("  python test_pipeline.py        # Test basic functions")
    print("")
    print("Required external tools for full demo:")
    print("  - FLASH (read merging)")
    print("  - bowtie2 (alignment)")
    print("  - samtools (BAM processing)")
    print("  - MAGeCK (counting)")
    print("")
    print("Python dependencies:")
    print("  - pandas, numpy, matplotlib, pysam, openpyxl")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        run_pipeline_demo()
