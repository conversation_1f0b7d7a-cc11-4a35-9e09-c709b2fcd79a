#!/usr/bin/env python2
# -*- coding: utf-8 -*-

"""
sgRNA Analysis Pipeline
A comprehensive pipeline for analyzing sgRNA library sequencing data
"""

from __future__ import division, print_function
import os
import sys
import re
import gzip
import argparse
import subprocess
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from collections import defaultdict
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def reverse_complement(seq):
    """Return reverse complement of DNA sequence"""
    complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
    return ''.join(complement.get(base, base) for base in reversed(seq.upper()))

def process_excel_to_csv(excel_file, output_csv):
    """
    Convert Excel file to CSV and return sgRNA length range
    """
    logger.info("Processing Excel file: %s" % excel_file)

    # Read Excel file
    df = pd.read_excel(excel_file)

    # Get first two columns (sgRNA ID and sequence)
    df_subset = df.iloc[:, :2]
    df_subset.columns = ['sgRNA_ID', 'sgRNA_sequence']

    # Save to CSV
    df_subset.to_csv(output_csv, index=False)

    # Calculate length range
    lengths = df_subset['sgRNA_sequence'].str.len()
    min_len = lengths.min()
    max_len = lengths.max()

    logger.info("sgRNA length range: %d-%d" % (min_len, max_len))
    logger.info("Saved library to: %s" % output_csv)

    return min_len, max_len

def run_seqkit_sample(r1_file, r2_file, output_dir, sample_name, num_reads):
    """
    Use seqkit to sample reads from FASTQ files
    """
    logger.info("Using seqkit to sample %d reads..." % num_reads)

    # Output file names
    sampled_r1 = os.path.join(output_dir, sample_name + '.R1.fastq.gz')
    sampled_r2 = os.path.join(output_dir, sample_name + '.R2.fastq.gz')

    # Sample R1
    cmd_r1 = ['seqkit', 'sample', '-n', str(num_reads), r1_file, '-o', sampled_r1]
    logger.info("Command: %s" % ' '.join(cmd_r1))
    subprocess.check_call(cmd_r1)

    # Sample R2
    cmd_r2 = ['seqkit', 'sample', '-n', str(num_reads), r2_file, '-o', sampled_r2]
    logger.info("Command: %s" % ' '.join(cmd_r2))
    subprocess.check_call(cmd_r2)

    logger.info("Seqkit sampling completed: %s, %s" % (sampled_r1, sampled_r2))
    return sampled_r1, sampled_r2

def run_flash(r1_file, r2_file, output_dir, sample_name):
    """
    Run FLASH to merge paired-end reads
    """
    logger.info("Running FLASH for read merging...")

    cmd = [
        'flash', r1_file, r2_file,
        '-m', '20',
        '-d', output_dir,
        '-o', sample_name,
        '-M', '1000',
        '--allow-outies',
        '-q'
    ]

    logger.info("Command: %s" % ' '.join(cmd))

    try:
        subprocess.check_call(cmd)
        logger.info("FLASH completed successfully")

        # Return expected output files
        extended_file = os.path.join(output_dir, sample_name + '.extendedFrags.fastq')
        not_combined_1 = os.path.join(output_dir, sample_name + '.notCombined_1.fastq')
        not_combined_2 = os.path.join(output_dir, sample_name + '.notCombined_2.fastq')

        return extended_file, not_combined_1, not_combined_2

    except subprocess.CalledProcessError as e:
        logger.error("FLASH failed: %s" % str(e))
        raise

def extract_sgrna_from_read(read_seq, read_qual, upstream, downstream, min_len, max_len):
    """
    Extract sgRNA sequence from a read using upstream and downstream flanking sequences
    Returns (extracted_seq, extracted_qual) or (None, None) if not found
    """
    # Try forward orientation
    for sgrna_len in range(min_len, max_len + 1):
        pattern = upstream + '(.{%d})' % sgrna_len + downstream
        match = re.search(pattern, read_seq)
        if match:
            start_pos = match.start(1)
            end_pos = match.end(1)
            extracted_seq = match.group(1)
            extracted_qual = read_qual[start_pos:end_pos]
            return extracted_seq, extracted_qual

    # Try reverse complement orientation
    upstream_rc = reverse_complement(upstream)
    downstream_rc = reverse_complement(downstream)

    for sgrna_len in range(min_len, max_len + 1):
        pattern = downstream_rc + '(.{%d})' % sgrna_len + upstream_rc
        match = re.search(pattern, read_seq)
        if match:
            start_pos = match.start(1)
            end_pos = match.end(1)
            extracted_seq = reverse_complement(match.group(1))
            extracted_qual = read_qual[start_pos:end_pos][::-1]  # Reverse quality scores
            return extracted_seq, extracted_qual

    return None, None

def process_fastq_file(fastq_file, upstream, downstream, min_len, max_len):
    """
    Process a FASTQ file and extract sgRNA sequences
    Returns list of (read_id, extracted_seq, extracted_qual) tuples
    """
    extracted_reads = []

    if fastq_file.endswith('.gz'):
        file_handle = gzip.open(fastq_file, 'rb')
    else:
        file_handle = open(fastq_file, 'r')

    try:
        while True:
            # Read FASTQ record (4 lines)
            header = file_handle.readline()
            if not header:
                break
            header = header.strip()
            if isinstance(header, bytes):
                header = header.decode('utf-8')

            sequence = file_handle.readline().strip()
            if isinstance(sequence, bytes):
                sequence = sequence.decode('utf-8')

            plus = file_handle.readline().strip()
            if isinstance(plus, bytes):
                plus = plus.decode('utf-8')

            quality = file_handle.readline().strip()
            if isinstance(quality, bytes):
                quality = quality.decode('utf-8')

            # Extract sgRNA
            extracted_seq, extracted_qual = extract_sgrna_from_read(
                sequence, quality, upstream, downstream, min_len, max_len
            )

            if extracted_seq:
                read_id = header[1:]  # Remove '@' prefix
                extracted_reads.append((read_id, extracted_seq, extracted_qual))

    finally:
        file_handle.close()

    return extracted_reads

def write_fastq_records(records, output_file):
    """
    Write extracted records to FASTQ file
    """
    with open(output_file, 'w') as f:
        for read_id, seq, qual in records:
            f.write("@%s\n%s\n+\n%s\n" % (read_id, seq, qual))

def process_not_combined_reads(not_combined_1, not_combined_2, upstream, downstream, min_len, max_len):
    """
    Process not combined reads by checking both R1 and R2 for each read ID
    """
    extracted_reads = []

    # First, try to extract from R1
    r1_reads = process_fastq_file(not_combined_1, upstream, downstream, min_len, max_len)
    extracted_ids = set(read_id for read_id, _, _ in r1_reads)
    extracted_reads.extend(r1_reads)

    # Load R2 reads into memory for lookup
    r2_reads_dict = {}
    if not_combined_2.endswith('.gz'):
        file_handle = gzip.open(not_combined_2, 'rb')
    else:
        file_handle = open(not_combined_2, 'r')

    try:
        while True:
            header = file_handle.readline()
            if not header:
                break
            header = header.strip()
            if isinstance(header, bytes):
                header = header.decode('utf-8')

            sequence = file_handle.readline().strip()
            if isinstance(sequence, bytes):
                sequence = sequence.decode('utf-8')

            plus = file_handle.readline().strip()
            if isinstance(plus, bytes):
                plus = plus.decode('utf-8')

            quality = file_handle.readline().strip()
            if isinstance(quality, bytes):
                quality = quality.decode('utf-8')

            read_id = header[1:]  # Remove '@' prefix
            r2_reads_dict[read_id] = (sequence, quality)

    finally:
        file_handle.close()

    # Process R1 reads that didn't yield results, check corresponding R2
    if not_combined_1.endswith('.gz'):
        file_handle = gzip.open(not_combined_1, 'rb')
    else:
        file_handle = open(not_combined_1, 'r')

    try:
        while True:
            header = file_handle.readline()
            if not header:
                break
            header = header.strip()
            if isinstance(header, bytes):
                header = header.decode('utf-8')

            sequence = file_handle.readline().strip()
            if isinstance(sequence, bytes):
                sequence = sequence.decode('utf-8')

            plus = file_handle.readline().strip()
            if isinstance(plus, bytes):
                plus = plus.decode('utf-8')

            quality = file_handle.readline().strip()
            if isinstance(quality, bytes):
                quality = quality.decode('utf-8')

            read_id = header[1:]  # Remove '@' prefix

            # Skip if already extracted from R1
            if read_id in extracted_ids:
                continue

            # Try R2 for this read ID
            if read_id in r2_reads_dict:
                r2_seq, r2_qual = r2_reads_dict[read_id]
                extracted_seq, extracted_qual = extract_sgrna_from_read(
                    r2_seq, r2_qual, upstream, downstream, min_len, max_len
                )

                if extracted_seq:
                    extracted_reads.append((read_id, extracted_seq, extracted_qual))

    finally:
        file_handle.close()

    return extracted_reads

def create_reference_fasta(csv_file, output_fasta):
    """
    Create reference FASTA file from CSV library
    """
    logger.info("Creating reference FASTA file...")

    df = pd.read_csv(csv_file)

    with open(output_fasta, 'w') as f:
        for _, row in df.iterrows():
            f.write(">%s\n%s\n" % (row['sgRNA_ID'], row['sgRNA_sequence']))

    logger.info("Reference FASTA created: %s" % output_fasta)

def run_bowtie2_alignment(reference_fasta, fastq_file, output_bam, sample_name):
    """
    Run bowtie2 alignment pipeline
    """
    logger.info("Running bowtie2 alignment...")

    # Build index
    index_name = sample_name + '_index'
    cmd_build = ['bowtie2-build', reference_fasta, index_name]
    logger.info("Command: %s" % ' '.join(cmd_build))
    subprocess.check_call(cmd_build)

    # Run alignment
    cmd_align = [
        'bowtie2', '-x', index_name, '-U', fastq_file, '--norc'
    ]

    # Convert to BAM
    cmd_samtools = ['samtools', 'view', '-bS', '-']
    logger.info("Command: %s | %s > %s" % (' '.join(cmd_align), ' '.join(cmd_samtools), output_bam))

    with open(output_bam, 'w') as bam_file:
        p1 = subprocess.Popen(cmd_align, stdout=subprocess.PIPE)
        p2 = subprocess.Popen(cmd_samtools,
                             stdin=p1.stdout, stdout=bam_file)
        p1.stdout.close()
        p2.communicate()

    logger.info("Alignment completed: %s" % output_bam)

def run_mageck_count(library_csv, sample_name, fastq_file):
    """
    Run MAGeCK count analysis
    """
    logger.info("Running MAGeCK count...")

    # 运行MAGeCK count，使用提取的FASTQ文件
    cmd = [
        'mageck', 'count',
        '-l', library_csv,
        '-n', sample_name,
        '--sample-label', sample_name,
        '--fastq', fastq_file,
        '--unmapped-to-file', sample_name
    ]

    logger.info("Command: %s" % ' '.join(cmd))

    try:
        subprocess.check_call(cmd)
        logger.info("MAGeCK count completed")
    except subprocess.CalledProcessError as e:
        logger.error("MAGeCK count failed: %s" % str(e))
        logger.info("Trying alternative MAGeCK command format...")

        # 尝试不同的MAGeCK命令格式
        cmd_alt = [
            'mageck', 'count',
            '-l', library_csv,
            '-n', sample_name,
            '--sample-label', sample_name,
            '--fastq', fastq_file
        ]

        logger.info("Command (alternative): %s" % ' '.join(cmd_alt))
        subprocess.check_call(cmd_alt)
        logger.info("MAGeCK count completed with alternative format")

def parse_mageck_log(log_file):
    """
    Parse MAGeCK log file to extract statistics
    """
    stats = {}

    with open(log_file, 'r') as f:
        for line in f:
            line = line.strip()
            if 'label' in line and not line.startswith('DEBUG'):
                stats['sample_name'] = line.split()[-1]
            elif 'reads' in line and not line.startswith('DEBUG') and not 'mappedreads' in line:
                # This matches "reads        9000" format, but not "mappedreads"
                parts = line.split()
                if len(parts) >= 2 and parts[-2] == 'reads':
                    stats['total_reads'] = int(parts[-1])
            elif 'mappedreads' in line:
                stats['mapped_reads'] = int(line.split()[-1])
            elif 'totalsgrnas' in line:
                stats['total_sgrnas'] = int(line.split()[-1])
            elif 'zerosgrnas' in line:
                stats['zero_sgrnas'] = int(line.split()[-1])

    return stats

def calculate_skew_ratio(count_file):
    """
    Calculate skew ratio from count file
    """
    logger.info("Calculating skew ratio...")

    df = pd.read_csv(count_file, sep='\t')
    counts = df.iloc[:, 2].values  # Third column contains counts
    counts = np.sort(counts)[::-1]  # Sort in descending order

    total_counts = np.sum(counts)
    cumulative = np.cumsum(counts)
    cumulative_pct = cumulative / total_counts

    # Find 10% and 90% positions
    idx_10 = np.where(cumulative_pct >= 0.1)[0][0] + 1
    idx_90 = np.where(cumulative_pct >= 0.9)[0][0] + 1

    skew_ratio = idx_90 / idx_10 if idx_10 > 0 else 0

    logger.info("Skew ratio: %.2f" % skew_ratio)
    return skew_ratio, idx_10, idx_90, counts

def plot_cumulative_distribution(counts, idx_10, idx_90, skew_ratio, output_file):
    """
    Plot cumulative distribution curve similar to example.jpg
    Creates a ROC-style curve with vertical lines and annotations
    """
    logger.info("Creating cumulative distribution plot...")

    total_counts = np.sum(counts)
    cumulative = np.cumsum(counts)
    cumulative_pct = cumulative / total_counts

    x_values = np.arange(1, len(counts) + 1)

    # Create figure with specific styling to match example.jpg
    plt.figure(figsize=(10, 8))

    # Plot main curve with blue color
    plt.plot(x_values, cumulative_pct, 'b-', linewidth=3, label='Cumulative Distribution')

    # Add vertical lines for 10% and 90% with different colors
    plt.axvline(x=idx_10, color='red', linestyle='--', linewidth=2,
                label='10%% position (gRNA #%d)' % idx_10)
    plt.axvline(x=idx_90, color='green', linestyle='--', linewidth=2,
                label='90%% position (gRNA #%d)' % idx_90)

    # Add horizontal reference lines
    plt.axhline(y=0.1, color='red', linestyle=':', alpha=0.5)
    plt.axhline(y=0.9, color='green', linestyle=':', alpha=0.5)

    # Add skew ratio annotation with prominent styling
    plt.text(0.65, 0.25, 'Skew Ratio = %.2f' % skew_ratio,
             transform=plt.gca().transAxes, fontsize=16, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8, edgecolor='black'))

    # Add additional annotations
    plt.text(idx_10, 0.05, '10%%\n(%d gRNAs)' % idx_10, ha='center', va='top',
             fontsize=10, bbox=dict(boxstyle='round', facecolor='red', alpha=0.3))
    plt.text(idx_90, 0.95, '90%%\n(%d gRNAs)' % idx_90, ha='center', va='bottom',
             fontsize=10, bbox=dict(boxstyle='round', facecolor='green', alpha=0.3))

    # Styling to match example.jpg
    plt.xlabel('gRNA Rank (sorted by read count)', fontsize=14, fontweight='bold')
    plt.ylabel('Cumulative Fraction', fontsize=14, fontweight='bold')
    plt.title('sgRNA Library Distribution Analysis\n(Cumulative Distribution Curve)',
              fontsize=16, fontweight='bold', pad=20)

    # Customize legend
    plt.legend(loc='center right', fontsize=12, framealpha=0.9)

    # Add grid with custom styling
    plt.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)

    # Set axis limits and ticks
    plt.xlim(0, len(counts))
    plt.ylim(0, 1)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)

    # Add border
    for spine in plt.gca().spines.values():
        spine.set_linewidth(1.5)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    logger.info("Plot saved: %s" % output_file)

def calculate_base_error_rate(mismatch_stats, count_file, library_csv, upstream, downstream):
    """
    Calculate base error rate from mismatch statistics
    """
    logger.info("Calculating base error rate...")

    # Calculate total mismatches
    total_mismatches = (
        mismatch_stats.get('1_mismatch', 0) * 1 +
        mismatch_stats.get('2_mismatch', 0) * 2 +
        mismatch_stats.get('3_mismatch', 0) * 3 +
        mismatch_stats.get('4_mismatch', 0) * 4 +
        mismatch_stats.get('5_mismatch', 0) * 5 +
        mismatch_stats.get('indel', 0) * 1
    )

    # Load library to get sequence lengths
    lib_df = pd.read_csv(library_csv)
    sgrna_lengths = {}
    for _, row in lib_df.iterrows():
        sgrna_lengths[row['sgRNA_ID']] = len(row['sgRNA_sequence'])

    # Load count file to calculate total bases
    count_df = pd.read_csv(count_file, sep='\t')

    total_bases = 0
    flanking_length = len(upstream) + len(downstream)

    for _, row in count_df.iterrows():
        sgrna_id = row.iloc[0]  # First column is sgRNA ID
        count = row.iloc[2]     # Third column is count

        if sgrna_id in sgrna_lengths:
            sgrna_len = sgrna_lengths[sgrna_id]
            total_bases += (sgrna_len + flanking_length) * count

    # Calculate error rate
    error_rate = total_mismatches / total_bases if total_bases > 0 else 0

    logger.info("Base error rate: %.4f" % error_rate)
    return error_rate

def generate_summary_report(stats, skew_ratio, error_rate, output_file):
    """
    Generate final summary report in Excel format
    """
    logger.info("Generating summary report...")

    # Calculate derived statistics
    sequencing_depth = stats['total_reads'] / stats['total_sgrnas']
    correct_sequence_rate = stats['mapped_reads'] / stats['total_reads']
    coverage_rate = (stats['total_sgrnas'] - stats['zero_sgrnas']) / stats['total_sgrnas']

    # Format numbers with commas and percentages
    data = [
        ['Sequencing Reads Amount', '{:,}'.format(stats['total_reads'])],
        ['Sequencing Depth', '{}X'.format(int(sequencing_depth))],
        ['Correct Sequence rate', '{:.2%}'.format(correct_sequence_rate)],
        ['Total gRNAs', str(stats['total_sgrnas'])],
        ['Zero gRNAs', str(stats['zero_sgrnas'])],
        ['Coverage_rate', '{:.2%}'.format(coverage_rate)],
        ['Skew Ration', '{:.2f}'.format(skew_ratio)],
        ['Base Error rate', '{:.2%}'.format(error_rate)]
    ]

    # Create DataFrame and save to Excel
    df = pd.DataFrame(data, columns=['Sample', 'Results'])

    # Ensure the output file has .xlsx extension for compatibility
    if output_file.endswith('.xls'):
        output_file = output_file.replace('.xls', '.xlsx')

    df.to_excel(output_file, index=False)

    logger.info("Summary report saved: %s" % output_file)

    # Also print to console
    print("\n=== Analysis Summary ===")
    for item in data:
        print("%-25s: %s" % (item[0], item[1]))
    print("========================\n")

def main():
    """
    Main pipeline function
    """
    parser = argparse.ArgumentParser(description='sgRNA Analysis Pipeline')
    parser.add_argument('excel_file', help='sgRNA library Excel file')
    parser.add_argument('r1_fastq', help='R1 FASTQ file (can be .gz)')
    parser.add_argument('r2_fastq', help='R2 FASTQ file (can be .gz)')
    parser.add_argument('flanking_sequences', help='Upstream-Downstream sequences (format: upstream-downstream)')
    parser.add_argument('-o', '--output_dir', default='.', help='Output directory (default: current directory)')
    parser.add_argument('-s', '--sample_name', help='Sample name (default: extracted from R1 filename)')
    parser.add_argument('-n', '--num_reads', type=int, default=10000, help='Number of reads to sample with seqkit (default: 10000)')
    parser.add_argument('-m', '--mislen', type=int, default=0, help='Mismatch length tolerance for sgRNA extraction (default: 0)')

    args = parser.parse_args()

    # Extract sample name from R1 filename if not provided
    if not args.sample_name:
        args.sample_name = os.path.basename(args.r1_fastq).split('.')[0]
        if args.sample_name.endswith('_R1') or args.sample_name.endswith('_1'):
            args.sample_name = args.sample_name[:-3]

    # Parse flanking sequences
    try:
        upstream, downstream = args.flanking_sequences.split('-', 1)
    except ValueError:
        logger.error("Flanking sequences must be in format: upstream-downstream")
        sys.exit(1)

    logger.info("Starting sgRNA analysis pipeline...")
    logger.info("Sample name: %s" % args.sample_name)
    logger.info("Upstream sequence: %s" % upstream)
    logger.info("Downstream sequence: %s" % downstream)
    logger.info("Number of reads to sample: %d" % args.num_reads)
    logger.info("Mismatch length tolerance: %d" % args.mislen)

    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)

    # Step 1: Process Excel to CSV
    library_csv = os.path.join(args.output_dir, 'lib.csv')
    min_len, max_len = process_excel_to_csv(args.excel_file, library_csv)

    # Apply mislen tolerance to length range
    adjusted_min_len = max(1, min_len - args.mislen)
    adjusted_max_len = max_len + args.mislen
    logger.info("Original sgRNA length range: %d-%d" % (min_len, max_len))
    logger.info("Adjusted sgRNA length range (with mislen=%d): %d-%d" % (args.mislen, adjusted_min_len, adjusted_max_len))

    # Step 2: Sample reads with seqkit
    sampled_r1, sampled_r2 = run_seqkit_sample(
        args.r1_fastq, args.r2_fastq, args.output_dir, args.sample_name, args.num_reads
    )

    # Step 3: Run FLASH on sampled reads
    extended_file, not_combined_1, not_combined_2 = run_flash(
        sampled_r1, sampled_r2, args.output_dir, args.sample_name
    )

    # Step 4: Extract sgRNA sequences
    logger.info("Extracting sgRNA sequences from merged reads...")
    combined_reads = process_fastq_file(extended_file, upstream, downstream, adjusted_min_len, adjusted_max_len)
    combined_output = os.path.join(args.output_dir, args.sample_name + '.combined.fq')
    write_fastq_records(combined_reads, combined_output)

    logger.info("Extracting sgRNA sequences from non-combined reads...")
    nocombined_reads = process_not_combined_reads(not_combined_1, not_combined_2, upstream, downstream, adjusted_min_len, adjusted_max_len)
    nocombined_output = os.path.join(args.output_dir, args.sample_name + '.nocombined.fq')
    write_fastq_records(nocombined_reads, nocombined_output)

    # Step 5: Combine FASTQ files
    final_fastq = os.path.join(args.output_dir, args.sample_name + '.fq')
    cmd_cat = ['cat', combined_output, nocombined_output]
    logger.info("Command: %s > %s" % (' '.join(cmd_cat), final_fastq))
    with open(final_fastq, 'w') as outfile:
        subprocess.call(cmd_cat, stdout=outfile)

    logger.info("Total extracted reads: %d" % (len(combined_reads) + len(nocombined_reads)))

    # Step 6: Create reference and run alignment
    reference_fasta = os.path.join(args.output_dir, 'reference_sgrna.fasta')
    create_reference_fasta(library_csv, reference_fasta)

    output_bam = os.path.join(args.output_dir, args.sample_name + '.bam')
    run_bowtie2_alignment(reference_fasta, final_fastq, output_bam, args.sample_name)

    # Step 7: Run MAGeCK count
    run_mageck_count(library_csv, args.sample_name, final_fastq)

    # Step 8: Parse MAGeCK log
    log_file = args.sample_name + '.log'
    stats = parse_mageck_log(log_file)

    # Step 9: Run mismatch analysis
    logger.info("Running mismatch analysis...")
    mismatch_csv = os.path.join(args.output_dir, 'mismatch_analysis.csv')

    # Import and run the mismatch analysis
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from sgrna_bowtie2_analysis_mis5 import main as mismatch_main, get_mismatch_stats

    # Convert BAM to SAM for mismatch analysis
    sam_file = os.path.join(args.output_dir, args.sample_name + '.sam')
    cmd_sam = ['samtools', 'view', '-h', output_bam]
    logger.info("Command: %s > %s" % (' '.join(cmd_sam), sam_file))
    with open(sam_file, 'w') as sam_out:
        subprocess.call(cmd_sam, stdout=sam_out)

    mismatch_main(sam_file, mismatch_csv, 'mismatch_plot.png')
    mismatch_stats = get_mismatch_stats(mismatch_csv)

    # Step 10: Calculate skew ratio and plot
    count_file = args.sample_name + '.count.txt'
    skew_ratio, idx_10, idx_90, counts = calculate_skew_ratio(count_file)

    plot_file = os.path.join(args.output_dir, args.sample_name + '.png')
    plot_cumulative_distribution(counts, idx_10, idx_90, skew_ratio, plot_file)

    # Step 11: Calculate base error rate
    error_rate = calculate_base_error_rate(mismatch_stats, count_file, library_csv, upstream, downstream)

    # Step 12: Generate final report
    report_file = os.path.join(args.output_dir, 'Sample.stat.xlsx')
    generate_summary_report(stats, skew_ratio, error_rate, report_file)

    logger.info("Pipeline completed successfully!")

if __name__ == '__main__':
    main()