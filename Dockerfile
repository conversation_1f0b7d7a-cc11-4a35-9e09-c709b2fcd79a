# sgRNA Analysis Pipeline Docker Container
FROM ubuntu:20.04

LABEL maintainer="CRISPRQC Team"
LABEL version="1.0"
LABEL description="sgRNA Analysis Pipeline with all dependencies"

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    build-essential \
    zlib1g-dev \
    libbz2-dev \
    liblzma-dev \
    libncurses5-dev \
    libcurl4-openssl-dev \
    libssl-dev \
    git \
    unzip \
    python2.7 \
    python2.7-dev \
    python-pip-whl \
    && rm -rf /var/lib/apt/lists/*

# Install pip for Python 2.7
RUN curl https://bootstrap.pypa.io/pip/2.7/get-pip.py -o get-pip.py && \
    python2.7 get-pip.py && \
    rm get-pip.py

# Install Python packages
RUN python2.7 -m pip install --no-cache-dir \
    pandas==0.24.2 \
    numpy==1.16.6 \
    matplotlib==2.2.5 \
    pysam==0.15.4 \
    openpyxl==2.6.4 \
    seaborn==0.9.1

# Install MAGeCK
RUN python2.7 -m pip install --no-cache-dir mageck==*******

# Install FLASH
WORKDIR /tmp
RUN wget https://ccb.jhu.edu/software/FLASH/FLASH-1.2.11-Linux-x86_64.tar.gz && \
    tar -xzf FLASH-1.2.11-Linux-x86_64.tar.gz && \
    mkdir -p /opt/flash && \
    cp FLASH-1.2.11-Linux-x86_64/flash /opt/flash/ && \
    chmod +x /opt/flash/flash && \
    rm -rf FLASH-1.2.11-Linux-x86_64*

# Install bowtie2
RUN wget https://github.com/BenLangmead/bowtie2/releases/download/v2.4.5/bowtie2-2.4.5-linux-x86_64.zip && \
    unzip bowtie2-2.4.5-linux-x86_64.zip && \
    mkdir -p /opt/bowtie2 && \
    cp bowtie2-2.4.5-linux-x86_64/bowtie2* /opt/bowtie2/ && \
    chmod +x /opt/bowtie2/bowtie2* && \
    rm -rf bowtie2-2.4.5-linux-x86_64*

# Install samtools
RUN wget https://github.com/samtools/samtools/releases/download/1.15.1/samtools-1.15.1.tar.bz2 && \
    tar -xjf samtools-1.15.1.tar.bz2 && \
    cd samtools-1.15.1 && \
    ./configure --prefix=/opt/samtools && \
    make && make install && \
    cd /tmp && \
    rm -rf samtools-1.15.1*

# Set up environment
ENV PATH="/opt/flash:/opt/bowtie2:/opt/samtools/bin:${PATH}"

# Copy pipeline scripts
COPY sgrna_analysis_pipeline.py /opt/
COPY sgrna_bowtie2_analysis_mis5.py /opt/

# Create working directory
WORKDIR /data

# Set default command
ENTRYPOINT ["python2.7", "/opt/sgrna_analysis_pipeline.py"]
